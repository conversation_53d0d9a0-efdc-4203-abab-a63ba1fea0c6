name: deploy
on:
  push:
    branches:
      - main
      - dev
  workflow_dispatch:
    inputs:
      logLevel:
        description: 'Log level'
        required: true
        default: 'warning'
      tags:
        description: 'Test scenario tags'
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'pnpm'
      - name: Install dependencies
        run: pnpm install
      - run: pnpm build:sit

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -p 22 -H dev-new.cel-la.store  >> ~/.ssh/known_hosts

      - name: Deploy
        run: sh manual.sh poly-zxz-cms ubuntu dev-new.cel-la.store "sudo docker-compose build" "sudo docker-compose stop" "sudo docker-compose up -d" "sudo docker ps -a"
