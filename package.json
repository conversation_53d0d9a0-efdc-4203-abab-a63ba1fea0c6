{"name": "poly-gzjg-cms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "build:sit": "tsc -b && vite build --mode sit", "lint": "eslint . --fix && tsc --noEmit", "preview": "vite preview", "lint:fix": "eslint . --fix --report-unused-disable-directives --max-warnings 0", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@ant-design/cssinjs": "^1.23.0", "@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.2", "antd": "^5.22.2", "axios": "^1.7.9", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "less": "^4.2.1", "moment": "^2.30.1", "nuqs": "^2.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.0.1", "react-use": "^17.6.0", "recharts": "^2.14.1", "swr": "^2.2.5", "tailwind-merge": "^2.5.5", "uuid": "^11.1.0", "valtio": "^2.1.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/node": "^22.10.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "prettier": "^3.4.1", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}, "lint-staged": {"*.{js,css,ts,tsx,jsx}": ["prettier --write", "eslint --fix"]}, "engines": {"node": ">=14.0.0", "pnpm": ">=7.0.0"}}