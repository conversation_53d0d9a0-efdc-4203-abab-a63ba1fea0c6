import { ApiResponse, service } from '~/lib/service'
import { BaseModel } from '~/models/base/baseModel'
import { PageParams } from '~/models/common/pageParams'
import { PageModel } from '~/models/common/pageModel'
import { Leave } from '~/models/group/meeting/approve/index'
import { Workflow } from '~/models/common/workflow'

/**
 * 获取会议请假审批列表
 * @returns
 */
export const queryLeaveList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Leave>>>('/leaves', { params })
}

/**
 * 会议请假详情
 * @param id 会议请假详情id
 * @returns
 */
export const queryLeave = (id: string, params?: Record<string, unknown>) => {
  return service.get<ApiResponse<Leave>>(`/leaves/${id}`, {
    params,
  })
}

/**
 * 获取会议请假审批流程
 * @param id 会议请假详情id
 * @returns
 */
export const queryLeaveFlows = (id: string) => {
  return service.get<ApiResponse<Workflow[]>>(`/leaves/${id}/workflows`)
}

/**
 * 获取会议工作流程审批操作权限
 * 获取参会确认操作情况
 * @param id 会议id
 * @returns
 */
export const queryLeaveFlowAction = (id: string) => {
  return service.get<ApiResponse<boolean>>(`/leaves/${id}/action/approve`)
}

/**
 * 会议请假流程审批
 * @param id 会议id
 * @param id 审批结果
 * @param reason 审批意见
 * @returns
 */
export const approveLeaveFlow = (
  id: string,
  result: string,
  reason: string
) => {
  return service.post<ApiResponse<BaseModel>>(
    `/leaves/${id}/workflows/approve`,
    {
      result,
      reason,
    }
  )
}
