import { ApiResponse, service } from '~/lib/service'
import { BaseModel } from '~/models/base/baseModel'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'
import { Meeting } from '~/models/group/meeting'
import { Attend } from '~/models/group/meeting/attend'
import { MeetingConfirm } from '~/models/group/meeting/confirm'
import { Workflow } from '~/models/common/workflow'

/**
 * 获取会议列表
 * @returns
 */
export const queryMeetingList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Meeting>>>('/meetings', { params })
}

// /**
//  * 创建会议
//  * @param name 自学主题
//  * @returns
//  */
// export const createMeeting = (name: string) => {
//   return service.post<ApiResponse<BaseModel>>('/meetings', { name })
// }

// /**
//  * 更新会议
//  * @param id 会议id
//  * @param name 自学主题
//  * @returns
//  */
// export const updateMeeting = (id: string, name: string) => {
//   return service.post<ApiResponse<BaseModel>>(`/meetings/${id}`, { name })
// }

/**
 * 查询会议
 * @param id 会议id
 * @returns
 */
export const queryMeeting = (id: string, params?: Record<string, unknown>) => {
  return service.get<ApiResponse<Meeting>>(`/meetings/${id}`, {
    params,
  })
}

/**
 * 删除会议
 * @param id 会议id
 * @returns
 */
export const delMeeting = (ids: string[]) => {
  return service.post<ApiResponse<BaseModel>>(`/meetings/delete`, { ids })
}

/**
 * 发起会议工作流程
 * @param id 会议id
 * @returns
 */
export const startMeetingFlow = (id: string) => {
  return service.get<ApiResponse<BaseModel>>(`/workflows/submit`, {
    params: { meeting_id: id },
  })
}

/**
 * 获取会议工作流程
 * @param id 会议id
 * @returns
 */
export const queryMeetingFlows = (id: string) => {
  return service.get<ApiResponse<Workflow[]>>(`/workflows/${id}`)
}

/**
 * 获取会议工作流程审批操作权限
 * @param id 会议id
 * @returns
 */
export const queryMeetingFlowAction = (id: string) => {
  return service.get<ApiResponse<boolean>>(`/workflows/action/approve`, {
    params: { meeting_id: id },
  })
}

/**
 * 会议工作流程审批
 * @param id 会议id
 * @param id 审批结果
 * @param reason 审批意见
 * @returns
 */
export const approveMeetingFlow = (
  id: string,
  result: string,
  reason: string
) => {
  return service.post<ApiResponse<BaseModel>>(`/workflows/approve`, {
    meeting_id: id,
    result,
    reason,
  })
}

/**
 * 发起参会确认
 * @param id 会议id
 * @returns
 */
export const startAttendMeetingAction = (id: string) => {
  return service.post<ApiResponse<BaseModel>>(`/meetings/${id}/confirms/start`)
}

/**
 * 获取参会确认操作情况
 * @param id 会议id
 * @returns
 */
export const queryMeetingConfirmAction = (id: string) => {
  return service.get<ApiResponse<MeetingConfirm>>(
    `/meetings/${id}/confirms/action`
  )
}

/**
 * 确认参会
 * @param id 会议id
 * @param id 确认结果
 * @param reason 请假原因
 * @returns
 */
export const confirmAttendMeeting = (id: string, params: Attend) => {
  return service.post<ApiResponse<BaseModel>>(
    `/meetings/${id}/confirms/action`,
    params
  )
}

/**
 * 获取参会确认情况列表
 * @param id 会议id
 * @returns
 */
export const queryMeetingConfirms = (id: string) => {
  return service.get<ApiResponse<MeetingConfirm[]>>(`/meetings/${id}/confirms`)
}
