import { ApiResponse, service } from '~/lib/service'
import { BaseModel } from '~/models/base/baseModel'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'
import { Learn } from '~/models/person/plan'

/**
 * 获取自学计划列表
 * @returns
 */
export const queryLearnList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Learn>>>('/learns', { params })
}

/**
 * 创建自学计划列表
 * @param name 自学主题
 * @returns
 */
export const createLearn = (name: string) => {
  return service.post<ApiResponse<BaseModel>>('/learns', { name })
}

/**
 * 更新自学计划列表
 * @param id 自学计划id
 * @param name 自学主题
 * @returns
 */
export const updateLearn = (id: string, name: string) => {
  return service.post<ApiResponse<BaseModel>>(`/learns/${id}`, { name })
}

/**
 * 查询自学计划
 * @param id 自学计划id
 * @returns
 */
export const queryLearn = (id: string) => {
  return service.get<ApiResponse<Learn>>(`/learns/${id}`)
}

/**
 * 删除自学计划
 * @param id 自学计划id
 * @returns
 */
export const delLearn = (ids: string[]) => {
  return service.post<ApiResponse<BaseModel>>(`/learns/delete`, { ids })
}
