import { ApiResponse, service } from '~/lib/service'
import { BaseModel } from '~/models/base/baseModel'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'
import { LearnResource } from '~/models/person/plan/list'

/**
 * 查询自学计划资料列表
 * @param id 自学计划id
 * @returns
 */
export const queryLearnResourceList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<LearnResource>>>(
    `/learns/${params.id}/resources`,
    {
      params,
    }
  )
}

/**
 * 上传自学计划资料
 * @param id 自学计划id
 * @param params 自学计划资料
 * @returns
 */
export const createLearnResource = (id: string, params: LearnResource) => {
  return service.post<ApiResponse<BaseModel>>(`/learns/${id}/resources`, params)
}

/**
 * 更新自学计划资料
 * @param id 自学计划资料id
 * @param params 自学计划资料
 * @returns
 */
export const updateLearnResource = (id: string, params: LearnResource) => {
  return service.post<ApiResponse<BaseModel>>(`/resources/${id}`, params)
}

/**
 * 查询自学计划资料
 * @param id 自学计划资料id
 * @returns
 */
export const queryLearnResource = (id: string) => {
  return service.get<ApiResponse<LearnResource>>(`/resources/${id}`)
}

/**
 * 删除自学计划资料
 * @param ids 自学计划资料id集合
 * @returns
 */
export const delLearnResources = (ids: string[]) => {
  return service.post<ApiResponse<LearnResource[]>>(`/resources/delete`, {
    ids: ids,
  })
}
