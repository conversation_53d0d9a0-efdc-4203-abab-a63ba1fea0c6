import { ApiResponse, service } from '~/lib/service'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'

export type Quarterly = {
  id: string
  subject: string
  publicationNo: string
  issueAt: string
  status: string
  publicationMaterials: {
    id: string
    name: string
    url: string
  }[]
  createdAt: string
  releaseAt: string
}

/**
 * 获取《理响》季刊列表
 * @returns
 */
export const getQuarterlyList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Quarterly>>>(
    '/achievement/quarterly',
    { params }
  )
}

/**
 * 创建《理响》季刊
 * @returns
 */
export const createQuarterly = (params: Quarterly) => {
  return service.post<ApiResponse<null>>('/achievement/quarterly', params)
}

/**
 * 获取《理响》季刊详情
 * @returns
 */
export const getQuarterlyDetail = (id: string) => {
  return service.get<ApiResponse<Quarterly>>(`/achievement/quarterly/${id}`)
}

/**
 * 删除《理响》季刊
 * @returns
 */
export const deleteQuarterly = (id: string) => {
  return service.post<ApiResponse<null>>('/achievement/quarterly/delete', {
    ids: [id],
  })
}

/**
 * 更新《理响》季刊
 * @returns
 */
export const updateQuarterly = (id: string, params: Partial<Quarterly>) => {
  return service.put<ApiResponse<null>>(`/achievement/quarterly/${id}`, params)
}
