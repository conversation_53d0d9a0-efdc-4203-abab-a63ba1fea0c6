import { ApiResponse, service } from '~/lib/service'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'
import { Learn } from '~/models/result/research-report'
import { BaseModel } from '~/models/base/baseModel'

/**
 * 获取调研报告与简报列表
 * @returns
 */
export const queryLearnList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Learn>>>('/achievement/report', {
    params,
  })
}

/**
 * 删除调研报告与简报列表
 * @param id 自学计划id
 * @returns
 */
export const delReport = (ids: string[]) => {
  return service.post<ApiResponse<BaseModel>>(`/achievement/report/delete`, {
    ids,
  })
}
