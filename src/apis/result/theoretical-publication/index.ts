import { ApiResponse, service } from '~/lib/service'
import { PageModel } from '~/models/common/pageModel'
import { PageParams } from '~/models/common/pageParams'
import { Learn } from '~/models/result/theoretical-publication'
import { BaseModel } from '~/models/base/baseModel'

/**
 * 获取调研报告与简报列表
 * @returns
 */
export const queryArticleList = (params: PageParams) => {
  return service.get<ApiResponse<PageModel<Learn>>>('/achievement/article', {
    params,
  })
}

/**
 * 删除调研报告与简报列表
 * @param id 自学计划id
 * @returns
 */
export const delArticle = (ids: string[]) => {
  return service.post<ApiResponse<BaseModel>>(`/achievement/article/delete`, {
    ids,
  })
}
