import { ApiResponse, service } from '~/lib/service'
import { PageModel } from '~/models/common/pageModel'
import { OrgListItem, User, UserForm, UserMgtList } from '~/models/user'

/**
 * 获取调研报告与简报列表
 * @returns
 */
export const queryUserList = () => {
  return service.get<ApiResponse<User[]>>('/users')
}

/**
 * 获取用户管理列表
 * @param params
 * @returns
 */
export const queryUserMgtList = (params: any) => {
  return service.get<ApiResponse<PageModel<UserMgtList>>>('/users/mgt', {
    params,
  })
}

/**
 * 获取组织树
 * @returns
 */
export const queryOrgTree = () => {
  return service.get<ApiResponse<OrgListItem[]>>('/orgs/tree')
}

/**
 * 获取用户详情
 */
export const queryUserDetail = (id: string) => {
  return service.get<ApiResponse<UserForm>>(`/users/${id}`)
}

/**
 * 添加用户
 */
export const addUser = (data: UserForm) => {
  return service.post<ApiResponse<User>>('/users', data)
}

/**
 * 编辑用户
 */
export const editUser = (data: UserForm) => {
  return service.put<ApiResponse<User>>(`/users/${data.id}`, data)
}

/**
 * 删除用户
 */
export const deleteUser = (id: string) => {
  return service.delete<ApiResponse<User>>(`/users/${id}`)
}
