// App.tsx
import React from 'react'

// 假设我们有以下几种角色
enum Role {
  ADMIN = 'sa',
  USER = 'user',
  GUEST = 'guest',
}

// 从 localStorage 获取用户信息
const storedUserInfo = localStorage.getItem('userInfo')
const userInfo = storedUserInfo
  ? JSON.parse(storedUserInfo)
  : { role: Role.GUEST } // 默认角色为访客

// 当前用户角色改为从 userInfo 中获取
const currentUserRole: Role = userInfo.role

// 定义按钮权限
const buttonPermissions: { [key in Role]?: string[] } = {
  [Role.ADMIN]: ['create', 'edit', 'delete'],
  [Role.USER]: ['view'],
  [Role.GUEST]: [],
}

// 按钮组件
interface PermissionButtonProps {
  action: string
  children: React.ReactNode
}

const PermissionButton: React.FC<PermissionButtonProps> = ({
  action,
  children,
}) => {
  const hasPermission = buttonPermissions[currentUserRole]?.includes(action)

  return hasPermission ? <button>{children}</button> : null
}

// 使用按钮组件
const App: React.FC = () => {
  return (
    <div>
      <PermissionButton action="create">创建</PermissionButton>
      <PermissionButton action="edit">编辑</PermissionButton>
      <PermissionButton action="delete">删除</PermissionButton>
      <PermissionButton action="view">查看</PermissionButton>
    </div>
  )
}

export default App
