import React, { ReactNode } from 'react'
import { Modal, Button, Space } from 'antd'
import type { ModalProps } from 'antd'

/**
 * PopUpComponent - 通用弹窗组件
 *
 * @description
 * 这是一个基于 Ant Design Modal 的可复用弹窗组件，支持基础弹窗、确认框、表单弹窗等多种类型。
 *
 * @example
 * // 1. 基础弹窗
 * const [visible, setVisible] = useState(false);
 *
 * <PopUpComponent
 *   title="标题"
 *   content="这是弹窗内容"
 *   onOk={() => setVisible(false)}
 *   onCancel={() => setVisible(false)}
 * />
 *
 * // 2. 确认框
 * <ConfirmPopUp
 *   title="确认删除"
 *   content="确定要删除这条记录吗？"
 *   onOk={async () => {
 *     await handleDelete();
 *     setVisible(false);
 *   }}
 *   onCancel={() => setVisible(false)}
 * />
 *
 * // 3. 表单弹窗
 * const [form] = Form.useForm();
 *
 * <FormPopUp
 *   title="编辑表单"
 *   onOk={async () => {
 *     const values = await form.validateFields();
 *     await handleSubmit(values);
 *     setVisible(false);
 *   }}
 *   onCancel={() => {
 *     setVisible(false);
 *     form.resetFields();
 *   }}
 * >
 *   <Form form={form}>
 *     {/* 表单内容 */
//  *   </Form >
//  * </FormPopUp >
//  *
//  * // 4. 自定义页脚
//  * <PopUpComponent
//  * title="自定义页脚"
//   * footer={ <Button>自定义按钮</Button> }
//  * onCancel={ () => setVisible(false) }
//  * >
//  * 自定义内容
//   * </PopUpComponent >
//  *
//  * @property { string } [title] - 弹窗标题，默认为"提示"
//  * @property { ReactNode } [content] - 弹窗内容
//  * @property { number | string } [width] - 弹窗宽度，默认为520
//  * @property { string } [okText] - 确认按钮文字，默认为"确定"
//  * @property { string } [cancelText] - 取消按钮文字，默认为"取消"
//  * @property { boolean } [showCancel] - 是否显示取消按钮，默认为true
//  * @property { boolean } [confirmLoading] - 确认按钮加载状态
//  * @property { () => void | Promise<void> } [onOk] - 点击确认的回调
//  * @property { () => void} [onCancel] - 点击取消的回调
//  * @property { ReactNode | null } [footer] - 自定义页脚，设为 null 时不显示页脚
//  * @property { ReactNode } [children] - 子元素，优先级低于content
//  */

interface PopUpProps extends Omit<ModalProps, 'onOk'> {
  title?: string
  content?: ReactNode
  width?: number | string
  okText?: string
  cancelText?: string
  showCancel?: boolean
  confirmLoading?: boolean
  onOk?: () => void | Promise<void>
  onCancel?: () => void
  footer?: ReactNode | null
  children?: ReactNode
}

const PopUpComponent: React.FC<PopUpProps> = ({
  title = '提示',
  content,
  width = 520,
  okText = '确定',
  cancelText = '取消',
  showCancel = true,
  confirmLoading = false,
  onOk,
  onCancel,
  footer,
  children,
  ...restProps
}) => {
  const handleOk = async () => {
    if (onOk) {
      await onOk()
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
  }

  const defaultFooter = (
    <Space>
      {showCancel && <Button onClick={handleCancel}>{cancelText}</Button>}
      <Button type="primary" loading={confirmLoading} onClick={handleOk}>
        {okText}
      </Button>
    </Space>
  )

  return (
    <Modal
      title={title}
      width={width}
      open={true}
      footer={footer === undefined ? defaultFooter : footer}
      onCancel={handleCancel}
      maskClosable={false}
      centered
      destroyOnClose
      {...restProps}
    >
      {content || children}
    </Modal>
  )
}

/**
 * ConfirmPopUp - 确认框类型的弹窗
 *
 * @description
 * 用于需要用户确认的操作，如删除、提交等。
 * 默认宽度为400px，适合简短的确认信息。
 *
 * @example
 * <ConfirmPopUp
 *   title="确认删除"
 *   content="确定要删除这条记录吗？"
 *   onOk={handleDelete}
 *   onCancel={() => setVisible(false)}
 * />
 */
export const ConfirmPopUp: React.FC<PopUpProps> = (props) => {
  return <PopUpComponent width={400} {...props} />
}

/**
 * FormPopUp - 表单类型的弹窗
 *
 * @description
 * 用于表单录入、编辑等场景。
 * 默认宽度为600px，适合较复杂的表单内容。
 *
 * @example
 * const [form] = Form.useForm();
 *
 * <FormPopUp
 *   title="编辑信息"
 *   onOk={async () => {
 *     const values = await form.validateFields();
 *     await handleSubmit(values);
 *     setVisible(false);
 *   }}
 *   onCancel={() => {
 *     setVisible(false);
 *     form.resetFields();
 *   }}
 * >
 *   <Form form={form}>
 *     <Form.Item name="name" label="姓名">
 *       <Input />
 *     </Form.Item>
 *   </Form>
 * </FormPopUp>
 */
export const FormPopUp: React.FC<PopUpProps> = (props) => {
  return <PopUpComponent width={600} {...props} />
}

export default PopUpComponent
