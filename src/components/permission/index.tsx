import { useAuthStore } from '~/store/auth'

interface PermissionProps {
  /** 角色集合 */
  roles: string[]
  /** 权限码集合 */
  codes: string[]
  children: React.ReactNode
  companyId?: string
}

const Permission: React.FC<PermissionProps> = ({
  roles,
  codes,
  companyId,
  children,
}) => {
  const { userInfo } = useAuthStore()

  const hasRole =
    userInfo!.roles?.filter((x) => roles?.includes(x) || x === 'sa')?.length > 0
  const hasPermission =
    userInfo!.permission_codes?.filter(
      (x) => codes?.includes(x) || x === 'SuperAdmin'
    )?.length > 0

  if (companyId && userInfo!.company_id !== companyId) {
    return null
  }

  return hasRole || hasPermission ? children : null
}

export default Permission
