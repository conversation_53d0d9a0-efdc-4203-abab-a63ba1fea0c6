import React from 'react'
import { Table, TableProps } from 'antd'

/**
 * 定义表格数据项类型
 */
export interface DataType {
  [key: string]: any // 允许任意字段
}

/**
 * 定义自定义表格组件的Props类型，继承AntD TableProps
 */
interface CustomTableProps extends Omit<TableProps<DataType>, 'onChange'> {
  /** 数据源 */
  dataSource: DataType[]
  total?: number
  onChange?: (page: number, pageSize: number) => void
}

const CustomTable: React.FC<CustomTableProps> = ({
  dataSource,
  total,
  onChange,
  columns,
  pagination,
  ...restProps
}) => {
  return (
    <>
      <Table
        columns={columns}
        dataSource={dataSource}
        {...restProps}
        rowKey={(record) => restProps.rowKey || record.id}
        className={`h-0 flex-1 ${restProps.className || ''}`}
        scroll={{ scrollToFirstRowOnChange: true, y: '100%' }}
        tableLayout="fixed"
        pagination={
          pagination != undefined
            ? pagination
            : {
                showQuickJumper: true,
                showSizeChanger: true,
                total: total,
                showTotal: (total, range) =>
                  `共 ${total} 条  本页显示第 ${range[0]} ~ ${range[1]} 条`,
                onChange: (page, pageSize) => {
                  onChange && onChange(page, pageSize)
                },
              }
        }
      />
      <style lang="less">
        {`
          .ant-spin-nested-loading{
            height: 100%;
          }

          .ant-spin-container{
            height: 100%;
            display: flex;
            flex-direction: column;

            .ant-table{
              height: 0;
              flex: 1;
              
              .ant-table-container {
                display: flex;
                flex-direction: column;

                .ant-table-body {
                  height: 100%;
                  // scrollbar-width: none;
                  // scrollbar-color: transparent transparent;

                  // &::-webkit-scrollbar {
                  //   display: none;
                  // }
                }
              }
              .ant-table-container, .ant-table-content, .ant-table-content > table {
                height: 100%;
                overflow: hidden;
              }

              .ant-table-content > table .ant-table-tbody {
                height: 100%;
              }

              .ant-table-placeholder > td {
                border-bottom: none !important;
              }
            }
          }

          .ant-pagination{
            margin-bottom: 0 !important;
          }

          .ant-pagination .ant-pagination-item{
            border-color: #202875 !important;
          }

          .ant-pagination-item-active a{
            color: #ffffff !important;
          }

          .ant-pagination .ant-select-selector, .ant-pagination-options-quick-jumper > input{
            border-color: #202875 !important;
          }
      `}
      </style>
    </>
  )
}
export default CustomTable
