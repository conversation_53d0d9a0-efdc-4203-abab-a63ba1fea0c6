import { Button, message, Upload, UploadFile, UploadProps } from 'antd'
import React, { useEffect, useState } from 'react'
import { ApiResponse, service } from '~/lib/service'

interface File {
  id: string
  name: string
  url: string
}

interface UploadFileProps extends Omit<UploadProps, 'onChange'> {
  title?: string
  value?: UploadFile[]
  onChange?: (fileList: UploadFile[]) => void
  maxCount?: number
  maxSize?: number // 单位：MB
  accept?: string
  multiple?: boolean
  showUploadList?: boolean
  customRequest?: (options: any) => void
  onPreview?: (file: UploadFile) => void
}

const FileUpload: React.FC<UploadFileProps> = ({
  title = '上传文档',
  value = [],
  onChange,
  maxCount = 1,
  maxSize = 1024,
  accept = '.doc, .docx, .xls, .pdf, .ppt,pptx, .png, .zip, .tar',
  multiple = maxCount > 1,
  showUploadList = true,
  disabled = false,
  ...restProps
}) => {
  const [uploading, setUploading] = useState(false)
  const [fileList, setFileList] = useState<UploadFile[]>(value)
  const [uploadFileCount, setUploadFileCount] = useState(0)
  let completeFileCount = 0

  useEffect(() => {
    value.length > 0 &&
      setFileList(
        value.map((item) => {
          return {
            ...item,
            name: item?.name
              ? item.name
              : item.url
                ? decodeURIComponent(
                    item.url.slice(
                      item.url.lastIndexOf('/') + 1,
                      item.url.lastIndexOf('.')
                    )
                  )
                : '',
          }
        })
      )
  }, [value])

  const acceptTypes = accept.split(',')
  const acceptTips = acceptTypes
    .map((item) => item.replace('.', '').trim())
    .join('、')

  // 处理文件上传前的验证
  const beforeUpload = (file: UploadFile, files: UploadFile[]) => {
    setUploadFileCount(files.length)

    // 文件类型验证
    if (accept !== '*') {
      const isAcceptType = acceptTypes.some((type) => {
        return file.name.toLowerCase().endsWith(type.trim().toLowerCase())
      })
      if (!isAcceptType) {
        completeFileCount++
        message.error(`文件 ${file.name} 类型格式错误!`)
        return false
      }
    }

    // 文件大小验证
    const isLessThanMaxSize = (file.size || 0) / 1024 / 1024 < maxSize
    if (!isLessThanMaxSize) {
      completeFileCount++
      message.error(`文件 ${file.name} 超出最大上传限制${maxSize}MB!`)
      return false
    }

    setUploading(true)

    return true
  }

  let files = [...fileList]
  //自定义上传方法
  const customRequest = async ({ file, onSuccess, onError }: any) => {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const response: ApiResponse<File> = await service.post(
        '/files',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )
      if (response.code !== 200001) {
        message.error(`文件${file.name} 上传失败：${response.message}`)
      } else {
        files = [...files, { ...response.data, uid: file.uid }]
        setFileList(files)
        onSuccess(response.data)
      }

      completeFileCount++
      if (uploadFileCount === completeFileCount) {
        message.success(`上传完成`)
        setUploadFileCount(0)
        completeFileCount = 0
        setUploading(false)
        onChange && onChange(files)
      }
    } catch (error) {
      completeFileCount++
      onError(error)
    }
  }

  return (
    <Upload
      customRequest={customRequest}
      beforeUpload={beforeUpload}
      accept={accept}
      multiple={multiple}
      maxCount={maxCount}
      showUploadList={showUploadList}
      fileList={fileList}
      {...restProps}
      disabled={false}
      onRemove={(file) => {
        files = files.filter((item) => item.url !== file.url)
        setFileList(files)
        onChange && onChange(files)
        return true
      }}
    >
      <Button
        loading={uploading}
        color="primary"
        variant="solid"
        size="large"
        className="secondary-btn-outlined"
        disabled={disabled || fileList.length >= maxCount}
      >
        {title}
      </Button>
      <p className="accept-tips mt-4">支持类型：{acceptTips}。</p>

      <style>{`
        .accept-tips {
          color: #999;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      `}</style>
    </Upload>
  )
}

export default FileUpload
