const { host, protocol } = window.location

export const isProd = import.meta.env.MODE === 'production'
// 服务base url
export const serviceBaseUrl = import.meta.env.VITE_SERVICE_BASE_URL
// api base url
export const apiBaseUrl = `${import.meta.env.VITE_API_BASE_URL}/api/v1`
// 国资监管平台client_id
export const client_id = import.meta.env.VITE_CLIENT_ID
// sso登录地址
export const loginUrl = `${serviceBaseUrl}/sso/polyOAuth/authorize?response_type=code&client_id=${client_id}&redirect_uri=${encodeURIComponent(`${protocol}//${host}/login`)}&scope=openid`
// sso登出地址
export const logoutUrl = `${serviceBaseUrl}/sso/polyOAuth/logout?client_id=${client_id}&post_logout_redirect_uri=${encodeURIComponent(
  loginUrl
)}`

export const localStorageTokenKey = 'poly-zxz-token'
