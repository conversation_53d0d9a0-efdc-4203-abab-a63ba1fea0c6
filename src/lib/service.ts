import { message } from 'antd'
import axios, { type AxiosRequestConfig } from 'axios'
import { apiBaseUrl, localStorageTokenKey, loginUrl } from '~/config'

export interface ApiResponse<T> {
  code: number
  message: string
  data: T
}

const instance = axios.create({
  baseURL: import.meta.env.MODE === 'development' ? '/api/v1' : apiBaseUrl,
  headers: {
    'Content-Type': 'application/json',
  },
})

instance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(localStorageTokenKey)
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const { response } = error

    if (!response) {
      // 网络错误或请求超时
      message.error('网络错误，请检查你的网络连接')
      return Promise.reject(error)
    }

    const { status, data } = response

    // 处理 HTTP 状态码
    switch (status) {
      case 400:
        message.error(data?.message || '请求错误')
        break
      case 401:
        message.warning('未授权，请重新登录')
        window.location.href = loginUrl
        break
      case 403:
        message.warning('没有权限访问该资源')
        break
      case 404:
        message.error('请求的资源未找到')
        break
      case 500:
        message.error('服务器内部错误')
        break
      default:
        message.error(data?.message || '未知错误')
        break
    }

    return Promise.reject(error)
  }
)

export const service = <T>(config: AxiosRequestConfig) =>
  instance.request<unknown, T>(config)

service.post = <T = unknown>(
  url: string,
  data?: object,
  config?: AxiosRequestConfig
): Promise<T> => {
  return instance.post(url, data, config)
}

service.get = <T = unknown>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  return instance.get(url, config)
}

service.put = <T = unknown>(
  url: string,
  data?: object,
  config?: AxiosRequestConfig
): Promise<T> => {
  return instance.put(url, data, config)
}

service.delete = <T = unknown>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  return instance.delete(url, config)
}

export const getMyApplyMeetings = () => {
  return service.get('/api/meetings/my-apply')
}

export const getMyApproveMeetings = () => {
  return service.get('/api/meetings/my-approve')
}
