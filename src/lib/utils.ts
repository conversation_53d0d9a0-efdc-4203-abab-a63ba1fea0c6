import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 *
 * @param func 包装的函数，类型为泛型 T，要求是返回值为 void 的函数
 * @param wait 延迟的时间，单位是毫秒
 * @param immediate 指定是否在延迟开始时立即执行函数
 * @returns
 */
export function debounce<T extends (...args: unknown[]) => void>(
  func: T,
  wait: number,
  immediate: boolean = false
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null

  return function (...args: Parameters<T>): void {
    const later = () => {
      timeout = null
      if (!immediate) {
        func(...args)
      }
    }

    const callNow = immediate && timeout === null

    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(later, wait)

    if (callNow) {
      func(...args)
    }
  }
}

export function isNil(value: unknown) {
  return value === null || value === undefined
}

export function getMeetingName(
  meeting?: Partial<{
    name: string
    year: number
    count: number
    meeting_name: string
    meeting_year: string
    meeting_count: string
    [key: string]: unknown
  }>,
  opts?: {
    nameKey?: string
    yearKey?: string
    countKey?: string
  }
) {
  const { nameKey = 'name', yearKey = 'year', countKey = 'count' } = opts || {}
  const year = meeting?.[yearKey]
  const count = meeting?.[countKey]
  const name = meeting?.[nameKey]
  let ret = ''
  if (!isNil(year) && year !== 0) {
    ret += `${year}年度`
  }
  if (!isNil(count) && count !== 0) {
    ret += `第${count}次`
  }
  if (!isNil(name)) {
    ret += name
  }
  return ret
}
