import { createRoot } from 'react-dom/client'
import { NuqsAdapter } from 'nuqs/adapters/react'
import './styles/index.css'
import { RouterProvider } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import {
  StyleProvider,
  legacyLogicalPropertiesTransformer,
} from '@ant-design/cssinjs'
import zhCN from 'antd/locale/zh_CN'
import dayjs from 'dayjs'
import { router } from './router/index.tsx'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')

const userAgent = navigator.userAgent.toLowerCase()

const chromeMatch = userAgent.match(/chrome\/(\d+)/)
const chromeVersion = chromeMatch ? parseInt(chromeMatch[1], 10) : null

// 判断是否需要使用 StyleProvider
const useStyleProvider = chromeVersion !== null && chromeVersion < 88

createRoot(document.getElementById('root')!).render(
  <div className="h-screen w-screen">
    <ConfigProvider locale={zhCN}>
      <NuqsAdapter>
        {!useStyleProvider ? (
          <RouterProvider router={router} />
        ) : (
          <StyleProvider
            hashPriority="high"
            transformers={[legacyLogicalPropertiesTransformer]}
          >
            <RouterProvider router={router} />
          </StyleProvider>
        )}
      </NuqsAdapter>
    </ConfigProvider>
  </div>
)
