/**
 * 会议
 */
export interface Meeting {
  id?: string
  name?: string
  status?: string
  start_at?: string
  end_at?: string
  room?: string
  topic?: string
  creator?: string
  creator_id?: string
  is_draft?: boolean
  created_at?: string
  updated_at?: string
  deleted_at?: string | null
  company_id?: string
  dept_id?: string
  members?: Member[]
  observers?: Observer[]
  staff?: Staff[]
  resources?: MeetingResource[]
  implement?: Implement
  is_qj?: boolean
  after?: {
    brief_name: string
    brief_link: string
    brief_file_id?: string
    summary_link: string
    summary_name: string
    summary_file_id?: string
  }
  supplements: {
    link: string
    name: string
    id: string
    user_id: string
    file_id?: string
  }[]
  next?: string
  year?: number
  count?: number
  is_plan?: string
}

export interface Member {
  id?: string
  meeting_id?: string
  user_id?: string
  status?: string
  reason?: string
  full_name?: string
}

export interface Observer {
  id?: string
  meeting_id?: string
  user_id?: string
  status?: string
  reason?: string
}

export interface Staff {
  id?: string
  full_name?: string
  user_id?: string
}

export interface MeetingResource {
  id?: string
  name?: string
  type?: string
  link?: string
  speaker?: string
  file_id?: string
  learn_id?: string
  learn_name?: string
}

export interface Implement {
  id?: string
  officer?: string
  officer_id?: string
  leader?: string
  leader_id?: string
  dept_leader?: string
  dept_leader_id?: string
  dept_auditor?: string
  dept_auditor_id?: string
  cooperate_dept_leader?: string
  cooperate_dept_leader_id?: string
  meeting_id?: string
}
