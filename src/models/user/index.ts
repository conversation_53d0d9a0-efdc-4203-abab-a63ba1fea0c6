export interface User {
  id?: string
  full_name?: string
  mobile?: string
  email?: string
  avatar?: string
  status?: boolean
  dept_id?: string | null
  position?: string
  roles?: string[]
  created_at?: string
  updated_at?: string
  deleted_at?: string | null
}

/**
 * 用户管理
 */
export interface UserMgtList {
  id: string
  full_name?: string
  mobile?: string
  role_list?: string[]
  company_list?: string[]
}

/**
 * 用户管理列表查询参数
 */
export interface UserMgtListParams {
  current: number
  pageSize: number
  full_name?: string
  mobile?: string
  role_name?: string
  company_id?: string
}

/**
 * 用户表单
 */
export interface UserForm {
  id: string
  full_name: string
  mobile: string
  company_id: string
  roles: string[]
  sub_roles: {
    org_id: string
    roles: string[]
  }[]
}

/**
 * 组织树
 */
export interface OrgListItem {
  id: string
  name: string
  level: number
  parent_id: string
  children?: OrgListItem[]
}
