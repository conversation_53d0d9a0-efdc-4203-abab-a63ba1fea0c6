import {
  Avatar,
  // Badge,
  ConfigProvider,
  Dropdown,
  Menu,
  type MenuProps,
  Space,
  Spin,
} from 'antd'
import { Link, Outlet, useMatches, useNavigate } from 'react-router-dom'
import { useMount } from 'react-use'
import { useEffect, useState } from 'react'
import { MenuItemType } from 'antd/lib/menu/interface'
import { BellOutlined } from '@ant-design/icons'
import { useMenuRoute } from '~/hooks/useMenuRoute'
import { initStore } from '~/store'
import { useAuthStore } from '~/store/auth'
import { logoutUrl } from '~/config'
import { menuRoutes, RouteConfig } from '~/router'
import headerBg from '~/assets/images/header-bg.jpg'
import logo from '~/assets/images/logo.svg'
import {
  fetchLeavesCount,
  fetchMeetingCount,
  fetchReportCount,
  fetchResearchCount,
  fetchMeetingWatchCount,
  useMeetingStore,
} from '~/store/meeting'

const userDropDownMenus: MenuProps['items'] = [
  {
    key: 'logout',
    label: '退出登录',
  },
]

function App() {
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState('')
  const { userInfo } = useAuthStore()
  const matches = useMatches()
  const navigate = useNavigate()
  const meetingStore = useMeetingStore()

  useMount(async () => {
    try {
      const isReady = await initStore()
      if (isReady) setIsReady(true)
      fetchResearchCount()
      fetchLeavesCount()
      fetchMeetingCount()
      fetchReportCount()
      fetchMeetingWatchCount()
    } catch (error) {
      console.error(error)
      setError('初始化项目失败')
    }
  })

  const [openKeys, setOpenKeys] = useState<string[]>([])
  useEffect(() => {
    const keys = matches
      .filter((match) => match.pathname !== '/')
      .map((match) => match.pathname.slice(1, match.pathname.length))
    setOpenKeys(keys)
  }, [matches])

  const [, setSelectedKey] = useMenuRoute(1)

  if (error !== '')
    return <div className="h-full items-center justify-center">{error}</div>
  if (!isReady) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-4 text-[#d32f2f]">
        <Spin size="large" />
        <p>正在初始化...</p>
        <style>{`
          .ant-spin-dot-holder{
            color: #d32f2f !important;
          }
      `}</style>
      </div>
    )
  }

  /**
   * 递归遍历路由取出菜单
   * @param routes 路由集合
   * @param name 路由上级名称
   */
  const traverseConstRoutes = (
    routes: RouteConfig[]
  ): MenuItemType[] | undefined => {
    const menus: MenuItemType[] = []
    for (const route of routes) {
      let menu = {} as MenuItemType
      if (route.meta && route.meta?.menu) {
        if (
          route.meta.auth &&
          route.meta.roles?.length &&
          !route.meta.roles.some((role) => userInfo?.roles?.includes(role))
        ) {
          // 没有权限的菜单不显示
          continue
        }
        menu = {
          key:
            route.meta.activeKey ||
            route.path?.slice(1, route.path.length) ||
            '',
          // label:
          //   route.meta.activeKey === 'group/meeting' ? (
          //     <div className="flex w-full items-center gap-2">
          //       {route.meta?.title}
          //       {meetingStore.count ? (
          //         <Badge count={meetingStore.count} />
          //       ) : null}
          //     </div>
          //   ) : (
          //     route.meta?.title || ''
          //   ),
          label: route.meta?.title || '',
          children:
            route.children && route.children.length > 0
              ? traverseConstRoutes(route.children)
              : undefined,
        } as MenuItemType

        menus.push(menu)
      }
    }
    return menus.length === 0 ? undefined : menus
  }

  const menus = traverseConstRoutes(menuRoutes)

  return (
    <div className="flex h-full flex-col">
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#d32f2f',
            colorErrorActive: '#d32f2f',
            colorErrorBg: '#f3c7c7',
            borderRadiusLG: 4,
            // colorPrimaryHover: '#424a90',
            // colorPrimaryActive: '#202875',
            // colorLink: '#202875',
          },
          components: {
            Menu: {
              itemColor: '#333333',
              itemSelectedColor: '#333333',
              itemSelectedBg: '#fdf5f5',
              itemActiveBg: '#fdf5f5',
              itemHoverBg: '#fdf5f5',
              itemMarginInline: 0,
            },
            Button: {
              colorPrimary: '#d32f2f',
              primaryShadow: '',
              // paddingInline: 10,
              // paddingInlineLG: 20,
              // defaultHoverBg: '#212a7c',
            },
            Table: {
              headerBg: '#fdf5f5',
              headerColor: '#666666',
              cellFontSize: 16,
            },
            Pagination: {
              itemActiveBg: '#212a7c',
            },
            Tabs: {
              cardBg: '#f2f2f2',
              cardGutter: 0,
              cardPadding: '17px 30px',
              horizontalMargin: '0',
            },
          },
        }}
      >
        <header className="flex h-[90px] flex-shrink-0 flex-grow-0 items-center gap-4 shadow-2xl">
          <h2 className="flex flex-shrink-0 flex-grow-0 items-center gap-2 pl-4 text-xl font-semibold">
            <img src={logo} height={50} />

            <span className="text-2xl text-[#d32f2f]">
              保利集团中心组学习信息化系统
            </span>
          </h2>
          <div
            className="flex h-full flex-1 items-center justify-end gap-4 bg-[#d32f2f] bg-[length:auto_100%] bg-no-repeat pr-4"
            style={{ backgroundImage: `url(${headerBg})` }}
          >
            <Link to="/todo" className="text-white">
              <BellOutlined className="mr-1" />
              我的待办（{meetingStore.totalCount}）
            </Link>
            <Dropdown
              menu={{
                items: userDropDownMenus,
                onClick: ({ key }) => {
                  if (key === 'logout') {
                    window.location.href = logoutUrl
                  }
                },
              }}
              trigger={['click']}
            >
              <a onClick={(e) => e.preventDefault()} className="cursor-pointer">
                <Space>
                  <Avatar src={userInfo?.picture} />
                  <span className="text-base text-white">{userInfo?.name}</span>
                </Space>
              </a>
            </Dropdown>
          </div>
        </header>
        <div className="flex h-0 flex-1">
          <div className="h-full w-[200px] flex-shrink-0 flex-grow-0 overflow-hidden overflow-y-auto shadow-2xl">
            <Menu
              onSelect={({ key }) => {
                setSelectedKey(key)
              }}
              // selectedKeys={[selectedKey]}
              defaultSelectedKeys={openKeys}
              defaultOpenKeys={openKeys}
              items={menus}
              mode="inline"
              className="h-full justify-center"
              onClick={({ key }) => {
                navigate(key)
              }}
            />
          </div>
          <main className="h-full flex-1 overflow-hidden overflow-y-auto bg-[#f2f2f2] p-4">
            <Outlet />
          </main>
        </div>
      </ConfigProvider>

      <style lang="less">
        {`
          .ant-menu-submenu-selected .ant-menu-submenu-title, .ant-menu-root > .ant-menu-item-selected.ant-menu-item-only-child{
            background-color: #d32f2f;
            
            .ant-menu-title-content{
              color: #ffffff;
              font-weight: 600;
            }

            .ant-menu-submenu-arrow{
              color: #ffffff;
            }

            &:hover{
              background-color: #d32f2f;
            }
          }
          .ant-menu-item-selected {
            background-color: #fdf5f5;
          }
      `}
      </style>
    </div>
  )
}

export default App
