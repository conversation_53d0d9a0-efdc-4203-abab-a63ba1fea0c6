import { Spin } from 'antd'
import { useNavigate } from 'react-router-dom'
import { useMount } from 'react-use'
import { loginUrl } from '~/config'
import { service } from '~/lib/service'
import { setToken } from '~/store/auth'

interface AuthResponse {
  data: {
    token?: string
  }
}

export function Login() {
  const navigate = useNavigate()
  useMount(async () => {
    const ossCode = new URLSearchParams(window.location.search).get('code')
    if (!ossCode) {
      window.location.href = loginUrl
      return
    }
    try {
      const res: AuthResponse = await service({
        method: 'POST',
        url: '/login',
        params: {
          code: ossCode,
        },
      })
      const token = res.data?.token
      if (token) {
        setToken(token)
        navigate('/group/meeting')
      }
    } catch (error) {
      console.error(error)
    }
  })
  return (
    <div className="flex h-full flex-col items-center justify-center gap-4">
      <Spin size="large" />
      <div className="text-blue-500">正在登录，请稍后...</div>
    </div>
  )
}
