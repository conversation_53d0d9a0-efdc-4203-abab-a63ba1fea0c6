import { create<PERSON><PERSON>er<PERSON><PERSON>er, Navigate, RouteObject } from 'react-router-dom'
import App from './App'
import { Login } from './Login'
import PersonDoc from '~/views/person/doc'
import PersonPlan from '~/views/person/plan'
import PersonPlanList from '~/views/person/plan/list'
import PersonPlanListEdit from '~/views/person/plan/list/edit'
import Meeting from '~/views/group/meeting'
import MeetingEdit from '~/views/group/meeting/edit'
import MeetingDetail from '~/views/group/meeting/detail'
import MeetingApprove from '~/views/group/meeting/approve'
import MeetingApproveDetail from '~/views/group/meeting/approve/detail'
import ResearchReport from '~/views/result/research-report'
import ResearchReportEdit from '~/views/result/research-report/edit'
import LiXiangQuarterly from '~/views/result/lixiang-quarterly'
import LiXiangQuarterlyEdit from '~/views/result/lixiang-quarterly/detail'
import TheoreticalPublication from '~/views/result/theoretical-publication'
import TheoreticalPublicationEdit from '~/views/result/theoretical-publication/edit'
import DataOverviewDashboard from '~/views/data-overview/dashboard'
import DataOverviewAnalysis from '~/views/data-overview/analysis'
import LearnPlanEdit from '~/views/data-overview/analysis/learn-plan/edit'
import LearnPlanDetail from '~/views/data-overview/analysis/learn-plan/detail'
import LearnRecordDetail from '~/views/data-overview/analysis/learn-record/detail'
import LearnSituationEdit from '~/views/data-overview/analysis/learn-situation/edit'
import LearnSituationDetail from '~/views/data-overview/analysis/learn-situation/detail'

import AnnualReport from '~/views/examine/annual-report'
import AnnualReportDetail from '~/views/examine/annual-report/detail'
import SitInOn from '~/views/examine/sit-in-on'
import MeetingInformation from '~/views/examine/sit-in-on/meetingInformation'
import AttendanceAuditChecklist from '~/views/examine/sit-in-on/attendanceAuditChecklist'
import ViewDetails from '~/views/examine/sit-in-on/detail'

import AnnualFeedback from '~/views/examine/annual-feedback'
import AnnualFeedbackDetail from '~/views/examine/annual-feedback/detail'
import AnnualFeedbackEdit from '~/views/examine/annual-feedback/edit'

import UserMgt from '~/views/user'
import UserEdit from '~/views/user/edit'

import { Todo } from '~/views/todo'
import DataSituation from '~/views/data-overview/analysis/data-situation'
import DataSituationDetail from '~/views/data-overview/analysis/data-situation/detail'
import { ReformRecord } from '~/views/examine/sit-in-on/reformRecord'

interface IMetaProps {
  [key: string]: any
  auth?: boolean // 是否需要权限
  roles?: string[] // 需要的角色
  title?: string // 菜单名
  menu?: boolean // 是否是菜单，不是菜单的会被过滤掉
  icon?: string // 菜单icon
  order?: number // 菜单排序 值越小越排在前面
  hidden?: boolean // 是否隐藏 是否在侧边栏展示
  activeKey?: string // 菜单激活时的key
}

interface IRouteMetaProps {
  meta?: IMetaProps
}

type RouteConfig = RouteObject & {
  meta?: IMetaProps
  children?: (RouteObject & IRouteMetaProps)[] // 重写children
}

const menuRoutes: RouteConfig[] = [
  {
    path: '/person',
    meta: {
      title: '个人自学',
      menu: true,
      order: 1,
    },
    children: [
      {
        path: '/person/plan',
        meta: {
          title: '自学计划',
          menu: true,
          order: 2,
        },
        children: [
          {
            index: true,
            element: <PersonPlan />,
            meta: {
              title: '自学计划',
            },
          },
          {
            path: '/person/plan/:id/list',
            meta: {
              title: '自学计划清单',
            },
            children: [
              {
                index: true,
                element: <PersonPlanList />,
                meta: {
                  title: '自学计划清单',
                },
              },
              {
                path: '/person/plan/:id/list/create',
                element: <PersonPlanListEdit />,
                meta: {
                  title: '上传资料',
                },
              },
              {
                path: '/person/plan/:id/list/update/:list_id',
                element: <PersonPlanListEdit />,
                meta: {
                  title: '编辑资料',
                },
              },
            ],
          },
        ],
      },
      {
        path: '/person/doc',
        element: <PersonDoc />,
        meta: {
          title: '资料文库',
          menu: true,
          order: 1,
        },
      },
    ],
  },
  {
    path: '/group',
    meta: {
      title: '集中学习',
      menu: true,
      order: 2,
      activeKey: 'group/meeting',
    },
    children: [
      {
        index: true,
        element: <Navigate to="/group/meeting" />,
      },
      {
        path: '/group/meeting',
        meta: {
          title: '会议列表',
        },
        children: [
          {
            index: true,
            element: <Meeting />,
            meta: {
              title: '会议列表',
            },
          },
          {
            path: '/group/meeting/create',
            element: <MeetingEdit />,
            meta: {
              title: '创建会议',
            },
          },
          {
            path: '/group/meeting/update/:id',
            element: <MeetingEdit />,
            meta: {
              title: '编辑会议',
            },
          },
          {
            path: '/group/meeting/release/:id',
            element: <MeetingEdit />,
            meta: {
              title: '发布会议',
            },
          },
          {
            path: '/group/meeting/supplement/:id',
            element: <MeetingEdit />,
            meta: {
              title: '会议补录',
            },
          },
          {
            path: '/group/meeting/:id',
            element: <MeetingDetail />,
            meta: {
              title: '会议详情',
            },
          },
          {
            path: '/group/meeting/approve',
            meta: {
              title: '会议审批',
            },
            children: [
              {
                index: true,
                element: <MeetingApprove />,
                meta: {
                  title: '会议审批',
                },
              },
              {
                path: '/group/meeting/approve/:id',
                element: <MeetingApproveDetail />,
                meta: {
                  title: '审批详情',
                },
              },
            ],
          },
        ],
      },
    ],
  },
  {
    path: '/result',
    meta: {
      title: '成果转化',
      menu: true,
      order: 3,
    },
    children: [
      {
        path: '/result/research-report',
        meta: {
          title: '专题调研',
          menu: true,
          order: 1,
        },
        children: [
          {
            index: true,
            element: <ResearchReport />,
            meta: {
              title: '专题调研',
            },
          },
          {
            path: '/result/research-report/create',
            element: <ResearchReportEdit />,
            meta: {
              title: '新增数据',
            },
          },
          {
            path: '/result/research-report/update/:id',
            element: <ResearchReportEdit />,
            meta: {
              title: '编辑',
            },
          },
        ],
      },
      {
        path: '/result/theoretical-publication',
        meta: {
          title: '理论文章',
          menu: true,
          order: 2,
        },
        children: [
          {
            index: true,
            element: <TheoreticalPublication />,
            meta: {
              title: '理论文章',
            },
          },
          {
            path: '/result/theoretical-publication/create',
            element: <TheoreticalPublicationEdit />,
            meta: {
              title: '新增数据',
            },
          },
          {
            path: '/result/theoretical-publication/update/:id',
            element: <TheoreticalPublicationEdit />,
            meta: {
              title: '编辑',
            },
          },
        ],
      },
      {
        path: '/result/lixiang-quarterly',
        meta: { title: '《理响》季刊', menu: true, order: 3 },
        children: [
          {
            index: true,
            element: <LiXiangQuarterly />,
            meta: { title: '《理响》季刊' },
          },
          {
            path: '/result/lixiang-quarterly/create',
            element: <LiXiangQuarterlyEdit />,
            meta: { title: '新建数据' },
          },
          {
            path: '/result/lixiang-quarterly/edit/:id',
            element: <LiXiangQuarterlyEdit />,
            meta: { title: '编辑' },
          },
          {
            path: '/result/lixiang-quarterly/detail/:id',
            element: <LiXiangQuarterlyEdit />,
            meta: { title: '查看' },
          },
          // {
          //   path: '/result/theoretical-publication/update/:id',
          //   element: <TheoreticalPublicationEdit />,
          //   meta: {
          //     title: '编辑',
          //   },
          // },
        ],
      },
    ],
  },
  {
    path: '/data-overview',
    meta: {
      title: '数据概览',
      menu: true,
      order: 3,
    },
    children: [
      {
        path: '/data-overview/dashboard',
        meta: {
          title: '数屏全景',
          menu: true,
          order: 1,
        },
        children: [
          {
            index: true,
            element: <DataOverviewDashboard />,
            meta: {
              title: '数屏全景',
            },
          },
        ],
      },
      {
        path: '/data-overview/analysis',
        meta: {
          title: '数据全解',
          menu: true,
          order: 2,
        },
        children: [
          {
            index: true,
            element: <DataOverviewAnalysis />,
            meta: {
              title: '数据全解',
            },
          },
          {
            path: '/data-overview/analysis/learn-plan/create',
            element: <LearnPlanEdit />,
            meta: {
              title: '创建全年学习计划',
            },
          },
          {
            path: '/data-overview/analysis/learn-plan/update/:id',
            element: <LearnPlanEdit />,
            meta: {
              title: '编辑全年学习计划',
            },
          },
          {
            path: '/data-overview/analysis/learn-plan/:id',
            element: <LearnPlanDetail />,
            meta: {
              title: '全年学习计划详情',
            },
          },
          {
            path: '/data-overview/analysis/learn-record/:id',
            element: <LearnRecordDetail />,
            meta: {
              title: '全年学习台账详情',
            },
          },
          {
            path: '/data-overview/analysis/learn-situation/update/:id',
            element: <LearnSituationEdit />,
            meta: {
              title: '编辑中心组成员学习情况',
            },
          },
          {
            path: '/data-overview/analysis/learn-situation/:id',
            element: <LearnSituationDetail />,
            meta: {
              title: '中心组成员学习情况详情',
            },
          },
          {
            path: '/data-overview/analysis/data-situation',
            element: <DataSituation />,
            meta: {
              title: '下级党委数据情况',
            },
          },
          {
            path: '/data-overview/analysis/data-situation/detail',
            element: <DataSituationDetail />,
            meta: {
              title: '下级党委数据情况详情',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/examine',
    meta: {
      title: '考核评价',
      menu: true,
      order: 5,
    },
    children: [
      {
        path: '/examine/annual-report',
        meta: {
          title: '年度报告',
          menu: true,
          order: 1,
        },
        children: [
          {
            index: true,
            element: <AnnualReport />,
            meta: {
              title: '年度报告总',
            },
          },
          {
            path: '/examine/annual-report/:id/detail',
            element: <AnnualReportDetail />,
            meta: {
              title: '年度报告详情',
            },
          },
        ],
      },
      {
        path: '/examine/sit-in-on',
        meta: {
          title: '列席旁听',
          menu: true,
          order: 1,
        },
        children: [
          {
            index: true,
            element: <SitInOn />,
            meta: { title: '列席旁听' },
          },
          {
            path: '/examine/sit-in-on/meetingInformation/:id/',
            element: <MeetingInformation />,
            meta: { title: '填写会议信息' },
          },
          {
            path: '/examine/sit-in-on/attendanceAuditChecklist/:id/',
            element: <AttendanceAuditChecklist />,
            meta: { title: '填写检查表' },
          },
          {
            path: '/examine/sit-in-on/detail/:id/',
            element: <ViewDetails />,
            meta: { title: '查看详情' },
          },
          {
            path: '/examine/sit-in-on/reformRecord/:id/',
            element: <ReformRecord />,
            meta: { title: '整改台账' },
          },
        ],
      },
      {
        path: '/examine/annual-feedback',
        meta: {
          title: '年度反馈',
          menu: true,
          order: 1,
        },
        children: [
          {
            index: true,
            element: <AnnualFeedback />,
            meta: {
              title: '年度反馈',
            },
          },
          {
            path: '/examine/annual-feedback/:dirId/:year/list',
            element: <AnnualFeedbackDetail />,
            meta: {
              title: '年度反馈文件',
            },
          },
          {
            path: '/examine/annual-feedback/:dirId/:year/:orgShortName/:id/detail/:type',
            element: <AnnualFeedbackEdit />,
            meta: {
              title: '年度反馈文件',
            },
          },
        ],
      },
    ],
  },
  {
    path: '/user-mgt',
    meta: {
      title: '用户管理',
      menu: true,
      auth: true,
      roles: ['sa'],
      order: 1,
    },
    children: [
      {
        index: true,
        element: <UserMgt />,
        meta: {
          title: '用户管理',
        },
      },
      {
        path: '/user-mgt/create',
        element: <UserEdit />,
        meta: {
          title: '新增用户',
        },
      },
      {
        path: '/user-mgt/update/:id',
        element: <UserEdit />,
        meta: {
          title: '编辑',
        },
      },
    ],
  },
  {
    path: '/todo',
    meta: {
      title: '我的待办',
      menu: false,
      auth: true,
    },
    children: [
      {
        index: true,
        element: <Todo />,
        meta: {
          title: '我的待办',
        },
      },
    ],
  },
]

const routes = [
  {
    path: '/',
    element: <App />,
    children: [
      {
        index: true,
        element: <Navigate to="/person/doc" />,
      },
      ...menuRoutes,
    ],
  },
  {
    path: '/login',
    element: <Login />,
  },
]

const router = createBrowserRouter(routes)

export { router, routes, menuRoutes }
export type { RouteConfig }
