import { proxy, useSnapshot } from 'valtio'
import { localStorageTokenKey } from '~/config'

interface UserInfo {
  company: string
  company_code: string
  company_id: string
  department: string
  department_id: string
  id: string
  is_group_company: boolean
  name: string
  phone_number: string
  picture: string
  preferred_username: string
  roles: string[]
  permission_codes: string[]
}

interface AuthState {
  token: string | null
  isLoggedIn: boolean
  userInfo: UserInfo | null
}

export const authStore = proxy<AuthState>({
  token: localStorage.getItem(localStorageTokenKey) || null,
  isLoggedIn: !!localStorage.getItem(localStorageTokenKey),
  userInfo: null,
})

export const setToken = (token: string) => {
  localStorage.setItem(localStorageTokenKey, token)
  authStore.token = token
  authStore.isLoggedIn = true
}

export const setUserInfo = (data: Partial<UserInfo>) => {
  authStore.userInfo = {
    ...authStore.userInfo,
    ...data,
  } as UserInfo
}

export const useAuthStore = () => useSnapshot(authStore)
