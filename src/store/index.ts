import { setUserInfo } from './auth'
import { service } from '~/lib/service'

export interface UserInfo {
  company: string
  company_code: string
  company_id: string
  department: string
  department_id: string
  id: string
  is_group_company: boolean
  name: string
  phone_number: string
  picture: string
  preferred_username: string
  role: string[]
}

interface UserInfoResponse {
  code: number
  data: UserInfo
  message: string
}

let isInitialized = false

export async function initStore() {
  if (isInitialized) return false
  isInitialized = true
  try {
    const res: UserInfoResponse = await service.get('/user-info')
    setUserInfo(res.data)
  } catch (error) {
    console.error(error)
    return false
  }
  return true
}
