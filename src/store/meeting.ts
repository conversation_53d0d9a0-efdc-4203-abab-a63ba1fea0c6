import { proxy, useSnapshot, subscribe } from 'valtio'
import { ApiResponse, service } from '~/lib/service'

interface MeetingState {
  count: number
  leavesCount: number
  reportCount: number
  researchCount: number
  totalCount: number
  meetingWatchCount: number
}

export const meetingStore = proxy<MeetingState>({
  count: 0,
  leavesCount: 0,
  reportCount: 0,
  researchCount: 0,
  totalCount: 0,
  meetingWatchCount: 0,
})
export const useMeetingStore = () => useSnapshot(meetingStore)

subscribe(meetingStore, () => {
  meetingStore.totalCount =
    meetingStore.count +
    meetingStore.leavesCount +
    meetingStore.reportCount +
    meetingStore.researchCount +
    meetingStore.meetingWatchCount
})

export async function fetchMeetingCount() {
  const res = await service.get<
    ApiResponse<{
      pager: {
        total: number
      }
    }>
  >('/todo/meeting', {
    params: {
      pageSize: 1,
    },
  })
  if (res.code !== 200001) return

  meetingStore.count = res.data?.pager?.total || 0
}

export async function fetchLeavesCount() {
  const res = await service.get<
    ApiResponse<{
      pager: {
        total: number
      }
    }>
  >('/todo/leaves', {
    params: {
      pageSize: 1,
    },
  })
  if (res.code !== 200001) return

  meetingStore.leavesCount = res.data?.pager?.total || 0
}

export async function fetchReportCount() {
  const res = await service.get<
    ApiResponse<{
      pager: {
        total: number
      }
    }>
  >('/todo/yearReport', {
    params: {
      pageSize: 1,
    },
  })
  if (res.code !== 200001) return

  meetingStore.reportCount = res.data?.pager?.total || 0
}

export async function fetchResearchCount() {
  const res = await service.get<
    ApiResponse<{
      pager: {
        total: number
      }
    }>
  >('/todo/report', {
    params: {
      pageSize: 1,
    },
  })
  if (res.code !== 200001) return

  meetingStore.researchCount = res.data?.pager?.total || 0
}

export async function fetchMeetingWatchCount() {
  const res = await service.get<
    ApiResponse<{
      pager: {
        total: number
      }
    }>
  >('/todo/meetingWatch', {
    params: {
      pageSize: 1,
    },
  })
  if (res.code !== 200001) return

  meetingStore.meetingWatchCount = res.data?.pager?.total || 0
}
