export interface MeetingDetail {
  // 会议标题部分
  id?: string
  createTime?: string
  status?: string

  // 会议信息部分
  startTime?: string
  endTime?: string
  location?: string
  attendees?: string
  observers?: string
  topic?: string

  // 角色信息部分
  roles?: {
    primaryResponsible?: string
    centerDirector?: string
    departmentHead?: string
    departmentDeputy?: string
    coordinator?: string
  }

  // 会议材料部分
  materials?: {
    meetingDocs?: Array<{
      title?: string
      description?: string
      files?: Array<{
        name: string
        url: string
      }>
    }>
    relatedDocs?: Array<{
      title?: string
      description?: string
      files?: Array<{
        name: string
        url: string
      }>
    }>
    learningDocs?: Array<{
      title?: string
      description?: string
      files?: Array<{
        name: string
        url: string
      }>
    }>
    otherDocs?: Array<{
      title?: string
      description?: string
      files?: Array<{
        name: string
        url: string
      }>
    }>
  }

  // 工作过程
  workflow?: Array<{
    title: string
    operator?: string
    status: string
    time: string
    comment?: string
  }>

  // 添加参会确认相关字段
  attendance?: {
    status?: 'pending' | 'confirmed' | 'leave' // 当前用户的参会状态
    confirmations?: Array<{
      name: string
      status?: 'empty' | 'confirmed' | 'leave' | 'absent' | 'notAttend'
    }>
  }
}

// 添加参会确认请求的类型
export interface AttendanceConfirmRequest {
  status?: 'attend' | 'leave'
  reason?: string
}
