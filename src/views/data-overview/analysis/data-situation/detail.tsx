import { Breadcrumb, Tabs } from 'antd'
import { useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import LearnPlanDetail from '../learn-plan/detail'
import LearnRecordDetail from '../learn-record/detail'
import LearnSituationEdit from '../learn-situation/edit'

const DataSituationDetail = () => {
  const searchParams = useSearchParams()[0]

  const year = searchParams.get('year')
  const company = searchParams.get('company')
  const id = searchParams.get('id')

  const [defaultActiveKey, setDefaultActiveKey] = useState<string>('learnPlan')
  const navigate = useNavigate()

  const breadcrumbs = {
    learnPlan: '全年学习计划',
    learnRecord: '全年学习台账',
    learnSituation: '中心组成员学习情况',
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '数据概览' },
          {
            title: '数据全解',
            href: '#',
            onClick: () => navigate('/data-overview/analysis'),
          },
          {
            title: '下级党委数据情况',
            href: '#',
            onClick: () =>
              navigate('/data-overview/analysis?active=dataSituation'),
          },
          { title: year + '年' },
          { title: company },
          { title: breadcrumbs[defaultActiveKey as keyof typeof breadcrumbs] },
        ]}
      />

      <Tabs
        type="card"
        className="mt-4 h-0 flex-1"
        tabBarGutter={0}
        defaultActiveKey={defaultActiveKey}
        onChange={(key) => setDefaultActiveKey(key)}
        items={[
          {
            key: 'learnPlan',
            label: <span className="text-base font-bold">全年学习计划</span>,
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnPlanDetail propsYear={year} propsId={id} />
              </div>
            ),
          },
          {
            key: 'learnRecord',
            label: <span className="text-base font-bold">全年学习台账</span>,
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnRecordDetail propsYear={year} propsId={id} />
              </div>
            ),
          },
          {
            key: 'learnSituation',
            label: (
              <span className="text-base font-bold">中心组成员学习情况</span>
            ),
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnSituationEdit propsYear={year} propsId={id} />
              </div>
            ),
          },
        ]}
      ></Tabs>
      <style lang="less">
        {`
          .ant-tabs-content-holder{
            height: 0 !important;
            flex: 1 !important;
            display: flex !important;
          }

          .ant-tabs-content {
            height: 100%;
          }

          .ant-tabs-tabpane{
            height: 100%;
          }
      `}
      </style>
    </div>
  )
}

export default DataSituationDetail
