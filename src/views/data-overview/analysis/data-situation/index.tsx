import { Button, Flex, Select } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { PageParams } from '~/models/common/pageParams'
import { flattenedData } from '~/utils'

interface ListItem {
  year: string
  company: string
  company_id: string
}

const DataSituation: React.FC = () => {
  const navigate = useNavigate()
  const [tableData, setTableData] = useState<ListItem[]>([])
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })
  const [companyList, setCompanyList] = useState<any[]>([])
  const [currentCompanyId, setCurrentCompanyId] = useState('')

  useEffect(() => {
    const getData = async () => {
      const res = await service.get<ApiResponse<[]>>('/orgs/son')
      if (res.code !== 200001) return

      setCompanyList(flattenedData(res.data) || [])
    }
    getData()
  }, [])

  const getTableData = async () => {
    setLoading(true)
    try {
      const params = {
        ...pageParams,
      }
      if (currentCompanyId) params.company_id = currentCompanyId
      const res = await service.get<
        ApiResponse<{ list: ListItem[]; pager: { total: number } }>
      >('/dataComplete/sonData', {
        params,
      })
      if (res.code !== 200001) return []

      setTotal(res.data.pager.total)
      setTableData(res.data.list)
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      width: 200,
      align: 'left',
    },
    {
      title: '党组织名称',
      dataIndex: 'company',
      key: 'company',
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 180,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <Flex wrap justify="center">
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() =>
              navigate(
                `data-situation/detail?year=${record.year}&company=${record.company}&id=${record.company_id}`
              )
            }
          >
            查看详情
          </Button>
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  useEffect(() => {
    getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageParams, currentCompanyId])

  return (
    <div className="flex h-0 flex-1 flex-col gap-2">
      <div className="flex items-center">
        <div className="flex-shrink-0 flex-grow-0">党组织名称：</div>
        <Select
          value={currentCompanyId}
          className="w-64"
          options={companyList.map((item) => ({
            value: item.id,
            label: item.name,
          }))}
          onChange={setCurrentCompanyId}
          showSearch
          filterOption={(input, option) =>
            option?.label.toLowerCase().includes(input)
          }
          allowClear
        />
      </div>
      <CustomTable
        key="company_id"
        columns={columns}
        dataSource={tableData}
        rowSelection={{ type: 'checkbox' }}
        total={total}
        loading={loading}
        onChange={(page, pageSize) => {
          setPageParams({ current: page, pageSize })
        }}
      ></CustomTable>
    </div>
  )
}

export default DataSituation
