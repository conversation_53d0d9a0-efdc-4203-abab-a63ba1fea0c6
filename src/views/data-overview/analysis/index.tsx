import { Breadcrumb, Tabs } from 'antd'
import { useQueryState } from 'nuqs'
import LearnPlan from './learn-plan'
import LearnRecord from './learn-record'
import LearnSituation from './learn-situation'
import DataSituation from './data-situation'

const DataOverviewAnalysis: React.FC = () => {
  const [activeKey, setActiveKey] = useQueryState('active', {
    defaultValue: 'learnPlan',
  })

  const breadcrumbs = {
    learnPlan: '全年学习计划',
    learnRecord: '全年学习台账',
    learnSituation: '中心组成员学习情况',
    dataSituation: '下级党委数据情况',
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '数据概览' },
          { title: '数据全解' },
          { title: breadcrumbs[activeKey as keyof typeof breadcrumbs] },
        ]}
      />
      <Tabs
        type="card"
        className="mt-4 h-0 flex-1"
        tabBarGutter={0}
        defaultActiveKey={activeKey}
        onChange={(key) => setActiveKey(key)}
        items={[
          {
            key: 'learnPlan',
            label: <span className="text-base font-bold">全年学习计划</span>,
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnPlan></LearnPlan>
              </div>
            ),
          },
          {
            key: 'learnRecord',
            label: <span className="text-base font-bold">全年学习台账</span>,
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnRecord></LearnRecord>
              </div>
            ),
          },
          {
            key: 'learnSituation',
            label: (
              <span className="text-base font-bold">中心组成员学习情况</span>
            ),
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <LearnSituation></LearnSituation>
              </div>
            ),
          },
          {
            key: 'dataSituation',
            label: (
              <span className="text-base font-bold">下级党委数据情况</span>
            ),
            children: (
              <div className="flex h-full flex-col bg-white p-4">
                <DataSituation></DataSituation>
              </div>
            ),
          },
        ]}
      ></Tabs>
      <style lang="less">
        {`
          .ant-tabs-content-holder{
            height: 0 !important;
            flex: 1 !important;
            display: flex !important;
          }

          .ant-tabs-content {
            height: 100%;
          }

          .ant-tabs-tabpane{
            height: 100%;
          }
      `}
      </style>
    </div>
  )
}

export default DataOverviewAnalysis
