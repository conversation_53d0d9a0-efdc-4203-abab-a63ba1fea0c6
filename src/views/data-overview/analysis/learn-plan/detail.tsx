import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import { ColumnsType } from 'antd/lib/table'
import { useEffect, useState } from 'react'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'

interface TableItem {
  ID?: string
  created_at?: string
  quarter?: string
  sort?: number
  subject?: string
}

interface Detail {
  id: string
  company_name: string
  items: TableItem[]
  year: number
}

const LearnPlanDetail: React.FC<{
  propsYear?: string | null
  propsId?: string | null
}> = ({ propsYear, propsId }) => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [tableData, setTableData] = useState<TableItem[]>([])
  const [year, setYear] = useState<number>()
  const [loading, setLoading] = useState(false)
  const [detail, setDetail] = useState<Detail>()

  const getLearnPlanDetails = async (id: string | undefined) => {
    if (!id && !propsId) {
      setTableData([])
      return
    }

    setLoading(true)

    try {
      const response = await service.get<ApiResponse<Detail>>(
        propsId
          ? `/dataComplete/studyPlan/detail?company_id=${propsId}&year=${propsYear}`
          : `/dataComplete/studyPlan/${id}`
      )

      if (response.code === 200001) {
        setTableData(response.data.items)
        setYear(response.data.year)
        setDetail(response.data)
      } else {
        message.error(response.message)
      }
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'sort',
      key: 'sort',
      width: 80,
      align: 'center',
    },
    {
      title: '季度',
      dataIndex: 'quarter',
      key: 'quarter',
      width: 200,
      align: 'center',
    },
    {
      title: '学习主题',
      dataIndex: 'subject',
      key: 'subject',
      minWidth: 200,
      align: 'center',
      render: (_, record: any) => <p className="text-left">{record.subject}</p>,
    },
  ] as ColumnsType<DataType>

  useEffect(() => {
    getLearnPlanDetails(id)
  }, [])

  return (
    <div className="flex h-full flex-col">
      {!propsId && (
        <Breadcrumb
          separator=">"
          items={[
            { title: '数据概览' },
            {
              title: '数据全解',
              href: '#',
              onClick: () =>
                navigate(`/data-overview/analysis?active=learn-plan`),
            },
            {
              title: '全年学习计划',
              href: '#',
              onClick: () =>
                navigate(`/data-overview/analysis?active=learn-plan`),
            },
            { title: '详情' },
          ]}
        />
      )}
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white">
        {!propsYear && (
          <p className="w-full border-b border-gray-200 p-4 font-semibold">
            {year}年{detail?.company_name}全年学习计划
          </p>
        )}
        <div className="flex h-0 flex-1 flex-col p-4">
          <Row>
            <Col span={2}></Col>
            <Col span={22}>{}</Col>
          </Row>

          <CustomTable
            columns={columns}
            dataSource={tableData}
            loading={loading}
            pagination={false}
            rowKey="ID"
          ></CustomTable>
        </div>
        <Flex justify="end" gap="small" className="w-full p-4">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            // onClick={() => navigate('/data-overview/analysis#learn-plan')}
            onClick={() => navigate(-1)}
          >
            返回
          </Button>
        </Flex>
      </div>
    </div>
  )
}

export default LearnPlanDetail
