import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  DatePicker,
  Flex,
  Form,
  Input,
  message,
  Select,
  Spin,
} from 'antd'
import { ColumnsType } from 'antd/lib/table'
import dayjs from 'dayjs'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import { v4 as uuidv4 } from 'uuid'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'

interface QuarterItem {
  ID: string
  quarter: string
  subject: string
  created_at: string
  sort: number
}

interface YearPlan {
  company_id: string
  created_at: string
  creator: string
  creator_id: string
  id: string
  items: QuarterItem[]
  year: number
}

const LearnPlanEdit: React.FC = () => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const { id } = useParams()
  const [tableData, setTableData] = useState<any[]>([])
  const [learnPlan, setLearnPlan] = useState<YearPlan>()
  const [loading, setLoading] = useState(false)

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_, _record, index) => index + 1,
    },
    {
      title: '季度',
      dataIndex: 'quarter',
      key: 'quarter',
      width: 200,
      align: 'center',
      render: (_, _record: any, index) => (
        <Form.Item
          label=""
          name={'quarter.' + index}
          rules={[{ required: true, message: '请选择季度' }]}
        >
          <Select
            options={[
              { value: '第一季度', label: '第一季度' },
              { value: '第二季度', label: '第二季度' },
              { value: '第三季度', label: '第三季度' },
              { value: '第四季度', label: '第四季度' },
            ]}
            allowClear
            placeholder="请选择季度"
          />
        </Form.Item>
      ),
    },
    {
      title: '学习主题',
      dataIndex: 'subject',
      key: 'subject',
      minWidth: 200,
      align: 'center',
      render: (_, _record: any, index) => (
        <div className="flex items-center">
          <Form.Item
            label=""
            name={'subject.' + index}
            rules={[{ required: true, message: '请输入学习主题' }]}
            className="my-auto w-full"
          >
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 3 }}
              placeholder="请输入学习主题"
            />
          </Form.Item>

          <Flex gap="middle" vertical>
            <PlusCircleOutlined
              style={{
                fontSize: '20px',
                color: '#212a7c',
                cursor: 'pointer',
              }}
              onClick={() => {
                const newTableData = [...tableData, { id: uuidv4() }]
                setTableData(newTableData)
                const nextIndex = newTableData.length - 1
                form.setFieldsValue({
                  [`quarter.${nextIndex}`]: undefined,
                  [`subject.${nextIndex}`]: undefined,
                })
              }}
            />
            {index > 0 && (
              <MinusCircleOutlined
                style={{
                  fontSize: '20px',
                  color: '#ed645d',
                  cursor: 'pointer',
                }}
                onClick={() => {
                  const values = form.getFieldsValue()
                  const newValues: Record<string, any> = {}

                  const newTableData = tableData.filter((_, i) => i !== index)

                  newTableData.forEach((_, i) => {
                    if (i >= index) {
                      newValues[`quarter.${i}`] = values[`quarter.${i + 1}`]
                      newValues[`subject.${i}`] = values[`subject.${i + 1}`]
                    } else {
                      newValues[`quarter.${i}`] = values[`quarter.${i}`]
                      newValues[`subject.${i}`] = values[`subject.${i}`]
                    }
                  })

                  setTableData(newTableData)
                  form.setFieldsValue(newValues)
                }}
              />
            )}
          </Flex>
        </div>
      ),
    },
  ] as ColumnsType<DataType>

  const extractQuarterSubjectPairs = (obj: any) => {
    const keys = Object.keys(obj).filter((key) => key.startsWith('quarter.'))

    return keys.map((key) => {
      const matches = /\d+$/.exec(key)
      const index = matches ? matches[0] : '0'

      return {
        quarter: obj[key],
        subject: obj[`subject.${index}`],
      }
    })
  }

  const convertArrayToFormObject = (arr: QuarterItem[]) => {
    return arr.reduce<Record<string, string | number>>((acc, item, index) => {
      acc[`quarter.${index}`] = item.quarter
      acc[`subject.${index}`] = item.subject
      return acc
    }, {})
  }

  const getLearnPlanDetail = async (id: string) => {
    setLoading(true)
    try {
      const response = await service.get<ApiResponse<YearPlan>>(
        `/dataComplete/studyPlan/${id}`
      )
      if (response.code === 200001) {
        setLearnPlan(response.data)

        const { items, year } = response.data
        const formObj = convertArrayToFormObject(items)

        setTableData(() =>
          Array.from({ length: response.data.items.length }, () => ({
            id: uuidv4(),
          }))
        )

        form.setFieldsValue({
          year: dayjs().year(year),
          ...formObj,
        })
      } else {
        message.error(response.message)
      }
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    if (id) {
      getLearnPlanDetail(id)
    } else {
      setTableData([{ id: '1' }])
    }
  })

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setLoading(true)

    const { year, ...rest } = values

    const submitData = {
      year: Number(dayjs(year).format('YYYY')),
      items: extractQuarterSubjectPairs(rest),
    }

    let response

    try {
      if (id) {
        response = await service.put<ApiResponse<null>>(
          `/dataComplete/studyPlan/${id}`,
          { ...learnPlan, ...submitData }
        )
      } else {
        response = await service.post<ApiResponse<null>>(
          '/dataComplete/studyPlan',
          submitData
        )
      }

      if (response.code === 200001) {
        message.success('保存成功')
      } else {
        message.error(response.message)
      }
    } finally {
      setLoading(false)
      navigate('/data-overview/analysis#learn-plan')
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '数据概览' },
          {
            title: '数据全解',
            href: '#',
            onClick: () =>
              navigate(`/data-overview/analysis?active=learn-plan`),
          },
          {
            title: '全年学习计划',
            href: '#',
            onClick: () =>
              navigate(`/data-overview/analysis?active=learn-plan`),
          },
          { title: id ? '编辑' : '新增' },
        ]}
      />

      <Spin spinning={loading}>
        <div className="mt-4 flex h-0 flex-1 flex-col bg-white">
          <p className="w-full border-b border-gray-200 p-4 font-semibold">
            {id ? '编辑' : '新增'}
          </p>
          <Form
            form={form}
            onFinish={handleSubmit}
            labelCol={{ span: 2 }}
            labelAlign="left"
            className="flex h-0 flex-1 flex-col p-4"
          >
            <Form.Item
              label="年度"
              name="year"
              rules={[{ required: true, message: '请选择年度' }]}
            >
              <DatePicker
                format="YYYY"
                style={{ width: '100%' }}
                placeholder="请选择年度"
                picker="year"
              />
            </Form.Item>

            <CustomTable
              key="id"
              columns={columns}
              dataSource={tableData}
              pagination={false}
            ></CustomTable>
          </Form>
          <Flex justify="end" gap="small" className="w-full p-4">
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="secondary-btn-outlined"
              onClick={() =>
                navigate('/data-overview/analysis?active=learn-plan')
              }
            >
              取消
            </Button>
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={() => form.submit()}
            >
              保存
            </Button>
          </Flex>
        </div>
      </Spin>
    </div>
  )
}

export default LearnPlanEdit
