import { ExclamationCircleFilled } from '@ant-design/icons'
import { Button, Flex, message, Modal } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Permission from '~/components/permission'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { PageParams } from '~/models/common/pageParams'

interface ListItem {
  id: string
  year: string
  creator: string
  created_at: string
  updated_at: string
}

const LearnPlan: React.FC = () => {
  const navigate = useNavigate()
  const { confirm } = Modal
  const [tableData, setTableData] = useState<ListItem[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })

  const getListData = async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: ListItem[]
          total: number
        }>
      >('/dataComplete/studyPlan', {
        params: { ...pageParams },
      })
      if (res.code !== 200001) {
        message.error(res.message)
        return []
      }
      setTotal(res.data.total)
      setTableData(res.data.list)
    } finally {
      setLoading(false)
    }
  }

  const deleteLearnPlanItem = async (id: string) => {
    setLoading(true)
    try {
      const response = await service.post<ApiResponse<null>>(
        '/dataComplete/studyPlan/delete',
        {
          ids: [id],
        }
      )
      if (response.code === 200001) {
        message.success('删除成功')
      } else {
        message.error(response.message)
      }
    } finally {
      getListData()
      setLoading(false)
    }
  }

  useEffect(() => {
    getListData()
  }, [pageParams])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      width: 100,
      align: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      width: 200,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200,
      align: 'left',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 220,
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Flex wrap justify="center">
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() => navigate(`learn-plan/update/${record.id}`)}
          >
            编辑
          </Button>
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() => navigate(`learn-plan/${record.id}`)}
          >
            查看详情
          </Button>
          <Button
            type="link"
            className="danger-btn-link"
            onClick={() => handleRowDelete(record.id)}
          >
            删除
          </Button>
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  const handleRowDelete = (id: string) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      onOk() {
        deleteLearnPlanItem(id)
      },
    })
  }

  return (
    <>
      <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
        <Flex className="mb-4" gap="small" wrap>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="primary-btn"
            onClick={() => navigate(`learn-plan/create`)}
          >
            创建全年学习计划
          </Button>
        </Flex>
      </Permission>
      <CustomTable
        className=""
        columns={columns}
        dataSource={tableData}
        rowSelection={{ type: 'checkbox' }}
        total={total}
        loading={loading}
        onChange={(page, pageSize) => {
          setPageParams({ current: page, pageSize })
        }}
      ></CustomTable>
    </>
  )
}

export default LearnPlan
