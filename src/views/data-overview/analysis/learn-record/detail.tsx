import { But<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lex, Spin, message } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

interface RecordDetail {
  whole: {
    totalMeeting: number
    totalStudyPlan: number
    totalMeetingDiscussion: number
    totalMeetingVacation: number
  }
  detail: any[]
}

const LearnRecordDetail: React.FC<{
  propsYear?: string | null
  propsId?: string | null
}> = ({ propsYear, propsId }) => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { userInfo } = useAuthStore()

  const [loading, setLoading] = useState(false)
  const [recordDetail, setRecordDetail] = useState<RecordDetail>({
    whole: {
      totalMeeting: 0,
      totalStudyPlan: 0,
      totalMeetingDiscussion: 0,
      totalMeetingVacation: 0,
    },
    detail: [],
  })

  const getRecordData = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<RecordDetail>>(
        '/dataComplete/learningLedger/statistics',
        {
          params: {
            year: propsYear ?? id,
            company_id: propsId ?? userInfo?.company_id,
          },
        }
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return {}
      }

      setRecordDetail(res.data)
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    getRecordData()
  })

  const specificSituations = (recordDetail.detail || []).map(
    (item: any, index) => {
      return (
        <tr key={index + item.topic}>
          <td className="table-content w-[5%] p-3">
            <p>{index + 1}</p>
          </td>
          <td className="table-content w-[7%] p-3">
            <p>{dayjs(item.start_at).format('MM-DD')}</p>
          </td>
          <td
            style={{ textAlign: 'left' }}
            className="table-content w-[20%] p-3"
          >
            <p>{item.topic}</p>
          </td>
          <td className="table-content w-[20%] whitespace-pre-line p-3 text-left">
            <p>{item.learning_topics}</p>
          </td>
          <td className="table-content w-[9%] p-3">
            <p>{item.meeting_member_count}</p>
          </td>
          <td className="table-content w-[9%] whitespace-pre-line p-3">
            <p>{item.meeting_member_name}</p>
          </td>
          <td className="table-content w-[10%] p-3">
            <p>{item.leave}</p>
          </td>
          <td className="table-content w-[10%] p-3">
            <p>{item.meeting_supplement_name}</p>
          </td>
          <td className="table-content w-[10%] p-3">
            <p>{item.is_plan}</p>
          </td>
        </tr>
      )
    }
  )

  return (
    <Spin spinning={loading}>
      <div className="flex h-full flex-col">
        {!propsId && (
          <Breadcrumb
            separator=">"
            items={[
              { title: '数据概览' },
              {
                title: '数据全解',
                href: '#',
                onClick: () => navigate(`/data-overview/analysis`),
              },
              {
                title: '全年学习台账',
                href: '#',
                onClick: () =>
                  navigate(`/data-overview/analysis?active=learnRecord`),
              },
              { title: id + '年' },
              { title: userInfo?.company },
            ]}
          />
        )}
        <div className="mt-4 flex h-0 flex-1 flex-col bg-white">
          {!propsId && (
            <p className="w-full border-b border-gray-200 p-4 font-semibold">
              {id}年{userInfo?.company}理论学习中心组成员学习情况
            </p>
          )}
          <div className="hide-scrollbar flex h-0 flex-1 flex-col overflow-auto overflow-x-hidden p-4">
            <table className="record-table w-full" border={1} align="center">
              <thead>
                <tr>
                  <th colSpan={12}>
                    <p className="table-title w-full bg-[#fdf5f5] p-3">
                      整体情况
                    </p>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="table-title w-[2/15] p-3">
                    <p>开展学习总次数</p>
                  </td>
                  <td className="table-content w-[1/15] p-3">
                    <p>{recordDetail.whole.totalMeeting}次</p>
                  </td>
                  <td className="table-title w-[2/15] p-3">
                    <p>
                      计划内学习次数
                      <span className="text-[#d32f2f]">（要求占比≧2/3）</span>
                    </p>
                  </td>
                  <td className="table-content w-[1/15] p-3">
                    <p>{recordDetail.whole.totalStudyPlan}次</p>
                  </td>
                  <td className="table-title w-[2/15] p-3">
                    <p>集体研讨次数</p>
                  </td>
                  <td className="table-content w-[1/15] p-3">
                    <p>{recordDetail.whole.totalMeetingDiscussion}次</p>
                  </td>
                  {/* <td className="table-title w-[2/15] p-3">
                    <p>学习议题</p>
                  </td>
                  <td className="table-content w-[1/15] p-3">
                    {recordDetail.detail.map(
                      (item: any, index: number) =>
                        item.learning_topics && (
                          <p key={index}>{item.learning_topics}</p>
                        )
                    )}
                  </td> */}
                  <td className="table-title w-[2/15] p-3">
                    <p>集体研讨占比</p>
                  </td>
                  <td className="table-content w-[1/15] p-3">
                    <p>
                      {(
                        (recordDetail.whole.totalMeetingDiscussion /
                          recordDetail.whole.totalMeeting) *
                        100
                      ).toFixed(2) + '%'}
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <table
              className="record-table mt-4 w-full"
              border={1}
              align="center"
            >
              <colgroup>
                <col style={{ width: '5%' }} />
                <col style={{ width: '7%' }} />
                <col style={{ width: '20%' }} />
                <col style={{ width: '20%' }} />
                <col style={{ width: '9%' }} />
                <col style={{ width: '9%' }} />
                <col style={{ width: '10%' }} />
                <col style={{ width: '10%' }} />
                <col style={{ width: '10%' }} />
              </colgroup>
              <thead>
                <tr>
                  <th colSpan={9}>
                    <p className="table-title w-full bg-[#fdf5f5] p-3">
                      具体情况
                    </p>
                  </th>
                </tr>
                <tr>
                  <th>
                    <p className="table-title w-full p-3">序列</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">日期</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">学习主题</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">学习议题</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">研讨人数</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">研讨成员</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">请假情况</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">是否补学</p>
                  </th>
                  <th>
                    <p className="table-title w-full p-3">是否计划内</p>
                  </th>
                </tr>
              </thead>
              <tbody>{specificSituations}</tbody>
            </table>
          </div>
          <Flex justify="end" gap="small" className="w-full p-4">
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="secondary-btn-outlined"
              onClick={() => navigate(-1)}
            >
              返回
            </Button>
          </Flex>
        </div>
        <style lang="less">{`
        .record-table{
          border: 1px solid #e9e9e9;
          border-collapse: collapse;

          th {
            text-align: center;
            border: 1px solid #e9e9e9;
            padding: 0;
          }

          td {
            border: 1px solid #e9e9e9;
            text-align: center;
            word-break: break-word;
          }

          tr {
            page-break-inside: avoid;
          }
        }

        .table-title {
          color: #666666;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }

        .table-content {
          color: #333333;
          font-family: PingFang SC;
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          text-underline-position: from-font;
          text-decoration-skip-ink: none;
        }
      `}</style>
      </div>
    </Spin>
  )
}

export default LearnRecordDetail
