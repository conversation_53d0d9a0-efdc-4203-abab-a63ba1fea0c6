import { But<PERSON>, Flex } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { PageParams } from '~/models/common/pageParams'

interface ListItem {
  year: string
  updated_at: string
}

const LearnRecord: React.FC = () => {
  const navigate = useNavigate()
  const [tableData, setTableData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })

  const getTableData = async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: ListItem[]
          total: number
        }>
      >('/dataComplete/learningLedger/l1', {
        params: { ...pageParams },
      })
      if (res.code !== 200001) return []
      setTotal(res.data.total)
      setTableData(res.data.list)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    getTableData()
  }, [pageParams])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 200,
      align: 'left',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <Flex wrap justify="center">
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() => navigate(`learn-record/${record.year}`)}
          >
            查看详情
          </Button>
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  return (
    <CustomTable
      columns={columns}
      dataSource={tableData}
      rowSelection={{ type: 'checkbox' }}
      total={total}
      loading={loading}
      onChange={(page, pageSize) => {
        setPageParams({ current: page, pageSize })
      }}
    ></CustomTable>
  )
}

export default LearnRecord
