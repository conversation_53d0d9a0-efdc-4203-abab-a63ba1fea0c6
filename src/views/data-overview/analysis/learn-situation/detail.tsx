import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, message } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { isNil } from '~/lib/utils'
import { useAuthStore } from '~/store/auth'

interface TableItem {
  name: string
  uid: string
  totalMeetingDiscussion: number
  leave: number
  meeting_supplement_name: string
  briefing: number
}

const LearnSituationDetail: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { userInfo } = useAuthStore()

  const [tableData, setTableData] = useState<TableItem[]>([])
  const [loading, setLoading] = useState(false)

  const getRecordData = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<TableItem[]>>(
        '/dataComplete/learningSituation/statistics',
        {
          params: {
            year: id,
            company_id: userInfo?.company_id,
          },
        }
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return {}
      }

      setTableData(res.data)
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    getRecordData()
  })

  const columns = [
    {
      title: '中心组成员',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '交流次数',
      dataIndex: 'totalMeetingDiscussion',
      key: 'totalMeetingDiscussion',
      minWidth: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '请假次数',
      dataIndex: 'leave',
      key: 'leave',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '是否完成补学',
      dataIndex: 'meeting_supplement_name',
      key: 'meeting_supplement_name',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
      render: (text, record) => {
        if (text === '否') {
          const { supplementCount, leave } = record
          if (isNil(supplementCount) || isNil(leave)) {
            return text
          }
          return `否，完成${supplementCount}/${leave}次`
        }
        return text
      },
    },
    {
      title: '专题调研',
      dataIndex: 'briefing',
      key: 'briefing',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
  ] as ColumnsType<DataType>

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '数据概览' },
          {
            title: '数据全解',
            href: '#',
            onClick: () => navigate(`/data-overview/analysis`),
          },
          {
            title: '中心组学习情况',
            href: '#',
            onClick: () =>
              navigate(`/data-overview/analysis?active=learnSituation`),
          },
          { title: id },
          { title: userInfo?.company },
        ]}
      />

      <div className="mt-4 flex h-0 flex-1 flex-col bg-white">
        <p className="w-full border-b border-gray-200 p-4 font-semibold">
          {id}年{userInfo?.company}理论学习中心组成员学习情况
        </p>

        <div className="flex h-0 flex-1 flex-col p-4">
          <CustomTable
            columns={columns}
            dataSource={tableData}
            pagination={false}
            loading={loading}
          ></CustomTable>
        </div>
        <Flex justify="end" gap="small" className="w-full p-4">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() => navigate(-1)}
          >
            返回
          </Button>
        </Flex>
      </div>
    </div>
  )
}

export default LearnSituationDetail
