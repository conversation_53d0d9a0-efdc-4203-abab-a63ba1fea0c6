import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Flex, Form, message, Spin } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { isNil } from '~/lib/utils'
import { useAuthStore } from '~/store/auth'

interface TableItem {
  name: string
  uid: string
  totalMeetingDiscussion: number
  leave: number
  meeting_supplement_name: string
  briefing: number
}

const LearnSituationEdit: React.FC<{
  propsYear?: string | null
  propsId?: string | null
}> = ({ propsYear, propsId }) => {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const { id } = useParams()
  const { userInfo } = useAuthStore()

  const [tableData, setTableData] = useState<TableItem[]>([])
  const [loading, setLoading] = useState(false)

  // const isEditable = userInfo?.roles.includes('qt_undertaker')

  const getSituationData = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<TableItem[]>>(
        '/dataComplete/learningSituation/statistics',
        {
          params: {
            year: propsYear ?? id,
            company_id: propsId ?? userInfo?.company_id,
          },
        }
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return {}
      }

      setTableData(res.data)
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    getSituationData()
  })

  const columns = [
    {
      title: '中心组成员',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '交流次数',
      dataIndex: 'totalMeetingDiscussion',
      key: 'totalMeetingDiscussion',
      minWidth: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '请假次数',
      dataIndex: 'leave',
      key: 'leave',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
    {
      title: '是否完成补学',
      dataIndex: 'meeting_supplement_name',
      key: 'meeting_supplement_name',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
      render: (text, record) => {
        if (text === '无需补学') return text
        const { supplementCount, leave } = record
        if (isNil(supplementCount) || isNil(leave)) {
          return text
        }
        return `否，完成${supplementCount}/${leave}次`
      },
      // render: (_, record) => (
      //   <Form.Item
      //     label=""
      //     name={['meeting_supplement_name', record.uid]}
      //     initialValue={record.meeting_supplement_name === '是' ? '是' : '否'}
      //     rules={[{ required: true, message: '请选择是否完成补学' }]}
      //   >
      //     <Select
      //       options={[
      //         { value: '是', label: '是' },
      //         { value: '否', label: '否' },
      //       ]}
      //       allowClear
      //       placeholder="请选择是否完成补学"
      //       disabled
      //       // disabled={!isEditable}
      //     />
      //   </Form.Item>
      // ),
    },
    {
      title: '专题调研',
      dataIndex: 'briefing',
      key: 'briefing',
      width: 200,
      align: 'center',
      onHeaderCell: () => {
        return {
          style: { backgroundColor: 'transparent', fontWeight: 'normal' },
          onClick: () => {},
        }
      },
    },
  ] as ColumnsType<DataType>

  const handleSubmit = async (values: any) => {
    const tempTableData = tableData.map((item) => {
      const meeting_supplement_name =
        values.meeting_supplement_name[item.uid] === '是' ? '是' : '否'
      return {
        ...item,
        meeting_supplement_name,
      }
    })

    const submitData = {
      item: tempTableData,
      year: Number(id),
      company_id: userInfo?.company_id,
    }

    try {
      const response = await service.post<ApiResponse<null>>(
        '/dataComplete/learningSituation/update',
        submitData
      )

      if (response.code !== 200001) {
        message.error(response.message)
        return
      }

      navigate('/data-overview/analysis#learn-situation')
    } catch (error) {
      console.error(error)
      message.error('保存失败')
    }
  }

  return (
    <Spin spinning={loading}>
      <div className="flex h-full flex-col">
        {!propsId && (
          <Breadcrumb
            separator=">"
            items={[
              { title: '数据概览' },
              {
                title: '数据全解',
                href: '#',
                onClick: () => navigate(`/data-overview/analysis`),
              },
              {
                title: '中心组学习情况',
                href: '#',
                onClick: () =>
                  navigate(`/data-overview/analysis?active=learnSituation`),
              },
              { title: id },
              { title: userInfo?.company },
            ]}
          />
        )}
        <Form className="mt-4 flex h-0 flex-1 flex-col bg-white">
          {!propsId && (
            <p className="w-full border-b border-gray-200 p-4 font-semibold">
              {id}年{userInfo?.company}理论学习中心组成员学习情况
            </p>
          )}
          <Form
            form={form}
            onFinish={handleSubmit}
            labelCol={{ span: 2 }}
            labelAlign="left"
            className="flex h-0 flex-1 flex-col p-4"
          >
            <CustomTable
              columns={columns}
              dataSource={tableData}
              pagination={false}
              key="uid"
            ></CustomTable>
          </Form>
          <Flex justify="end" gap="small" className="w-full p-4">
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="secondary-btn-outlined"
              onClick={() =>
                navigate('/data-overview/analysis?active=learnSituation')
              }
            >
              返回
            </Button>
            {/* <Button
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={() => form.submit()}
            >
              保存
            </Button> */}
          </Flex>
        </Form>
      </div>
    </Spin>
  )
}

export default LearnSituationEdit
