import { But<PERSON>, Flex } from 'antd'
import { ColumnsType } from 'antd/lib/table'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { PageParams } from '~/models/common/pageParams'

interface ListItem {
  year: string
  updated_at: string
}

const LearnSituation: React.FC = () => {
  const navigate = useNavigate()
  const [tableData, setTableData] = useState<ListItem[]>([])
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })

  const getTableData = async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: ListItem[]
          total: number
        }>
      >('/dataComplete/learningSituation/l1', {
        params: { ...pageParams },
      })
      if (res.code !== 200001) return []
      setTotal(res.data.total)
      setTableData(res.data.list)
    } finally {
      setLoading(false)
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 200,
      align: 'left',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 180,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <Flex wrap justify="center">
          {/* {userInfo?.roles.includes('qt_undertaker') && (
            <Button
              type="link"
              className="primary-btn-link"
              onClick={() => navigate(`learn-situation/update/${record.year}`)}
            >
              编辑
            </Button>
          )} */}

          <Button
            type="link"
            className="primary-btn-link"
            onClick={() => navigate(`learn-situation/${record.year}`)}
          >
            查看详情
          </Button>
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  useEffect(() => {
    getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageParams])

  return (
    <CustomTable
      className=""
      columns={columns}
      dataSource={tableData}
      rowSelection={{ type: 'checkbox' }}
      total={total}
      loading={loading}
      onChange={(page, pageSize) => {
        setPageParams({ current: page, pageSize })
      }}
    ></CustomTable>
  )
}

export default LearnSituation
