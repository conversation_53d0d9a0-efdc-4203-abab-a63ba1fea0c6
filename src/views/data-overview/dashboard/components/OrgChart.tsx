import { Col, message, Row } from 'antd'
import { useEffect, useRef, useState } from 'react'
import { type Org } from '..'
import logoSvg from '~/assets/images/logo.svg'
import { ApiResponse, service } from '~/lib/service'
import { cn, debounce } from '~/lib/utils'
import { useAuthStore } from '~/store/auth'

interface OrgChatProps {
  data: Org[]

  onSelect?: (id: string) => void
  current: string | undefined
}

export function OrgChat({ data, onSelect, current }: OrgChatProps) {
  const orgWrapRef = useRef<HTMLDivElement>(null)
  const [subOrgColSpan, setSubOrgColSpan] = useState(12)
  const [showSubOrgs, setShowSubOrgs] = useState(false)
  const { userInfo } = useAuthStore()

  const initOrg = {
    id: userInfo?.company_id || '',
    name: userInfo?.company || '',
    parent_id: '',
    children: [],
  }

  const [firstOrg, setFirstOrg] = useState<Org>(initOrg)

  const getOrgDetail = async (id: string) => {
    if (!id) return
    try {
      const res = await service.get<ApiResponse<Org>>(`/orgs/org/${id}`)
      if (res.code !== 200001) {
        message.error(res.message)
        return
      }
      setFirstOrg(res.data)
    } catch (error) {
      message.error('获取机构详情失败')
      console.log(error)
    }
  }

  useEffect(() => {
    if (!orgWrapRef.current) return
    const observer = new ResizeObserver(
      debounce((entries) => {
        const width = (entries as ResizeObserverEntry[])[0].contentRect.width
        switch (true) {
          case width < 768:
            setSubOrgColSpan(12)
            break
            break
          case width < 1200:
            setSubOrgColSpan(6)
            break
          case width < 1600:
            setSubOrgColSpan(4)
            break
          default:
            setSubOrgColSpan(4)
        }
        setShowSubOrgs(true)
      }, 300)
    )
    observer.observe(orgWrapRef.current)
    return () => {
      observer.disconnect()
    }
  }, [])
  const rowCols = Math.min(24 / subOrgColSpan, data.length)

  return (
    <div ref={orgWrapRef}>
      <button
        style={{ display: firstOrg.id === userInfo?.company_id ? 'none' : '' }}
        className="text-red-600"
        onClick={() => {
          onSelect?.(firstOrg?.parent_id || '')
          getOrgDetail(firstOrg?.parent_id || '')
        }}
      >
        &lt; 返回上一层
      </button>
      <div className="flex justify-center">
        <div
          className={cn(
            'flex h-24 w-80 cursor-pointer flex-col items-center justify-center gap-2 rounded-xl p-2 text-white',
            current === firstOrg?.id ? 'bg-red-600' : 'bg-[#D32F2F99]'
          )}
        >
          <img src={logoSvg} className="h-7 w-7 brightness-0 invert" />
          <span className="text-xl">{firstOrg?.name}</span>
        </div>
      </div>
      {showSubOrgs && data && data.length > 0 && (
        <>
          <div className="relative">
            <Row
              gutter={12}
              className="relative justify-center after:absolute after:bottom-1/2 after:left-1/2 after:top-0 after:-ml-[1px] after:w-[2px] after:bg-[#D32F2F]"
            >
              {new Array(rowCols).fill(null).map((_, index) => {
                // 第一列
                const firstCol = index === 0
                // 最后一列
                const lastCol = index === rowCols - 1
                return (
                  <Col
                    span={subOrgColSpan}
                    key={index}
                    className="relative h-24"
                  >
                    {!firstCol && (
                      <div className="absolute bottom-12 left-0 right-1/2 h-[2px] bg-[#D32F2F]"></div>
                    )}
                    <div className="absolute bottom-0 left-1/2 top-12 -ml-[1px] -mt-[2px] w-[2px] bg-[#D32F2F]"></div>
                    {!lastCol && (
                      <div className="absolute bottom-12 left-1/2 right-0 h-[2px] bg-[#D32F2F]"></div>
                    )}
                  </Col>
                )
              })}
            </Row>
            <Row gutter={[16, 16]} className="justify-center">
              {data.map((org) => {
                return (
                  <Col span={subOrgColSpan} key={org.id} className="min-h-16">
                    <div
                      className={cn(
                        'flex h-full cursor-pointer flex-col items-center justify-center gap-2 rounded-xl p-2 text-white',
                        current === org.id ? 'bg-red-600' : 'bg-[#D32F2F99]'
                      )}
                      onClick={() => {
                        onSelect?.(org.id)
                        getOrgDetail(org.id)
                      }}
                    >
                      <span className="text-xl">{org.name}</span>
                    </div>
                  </Col>
                )
              })}
            </Row>
          </div>
        </>
      )}
    </div>
  )
}
