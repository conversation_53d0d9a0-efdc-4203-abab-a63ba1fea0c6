import ReactECharts from 'echarts-for-react'
import { CSSProperties } from 'react'

const typeOptions = {
  warning: {
    checkSvg:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjIwIiBmaWxsPSIjQ0U5NDJEIiBmaWxsLW9wYWNpdHk9IjAuMSIvPg0KPHBhdGggZD0iTTI0LjE3NCAxNi40NTA5SDIzLjEyNzJDMjIuODk5NSAxNi40NTA5IDIyLjY4MyAxNi41NjAzIDIyLjU0OSAxNi43NDc4TDE5LjA0MDEgMjEuNjEzOUwxNy40NTA4IDE5LjQwODVDMTcuMzE2OSAxOS4yMjMyIDE3LjEwMjYgMTkuMTExNiAxNi44NzI3IDE5LjExMTZIMTUuODI1OEMxNS42ODA3IDE5LjExMTYgMTUuNTk1OSAxOS4yNzY4IDE1LjY4MDcgMTkuMzk1MUwxOC40NjIgMjMuMjUyM0MxOC41Mjc3IDIzLjM0NCAxOC42MTQzIDIzLjQxODcgMTguNzE0NyAyMy40NzAyQzE4LjgxNSAyMy41MjE4IDE4LjkyNjIgMjMuNTQ4NyAxOS4wMzkgMjMuNTQ4N0MxOS4xNTE4IDIzLjU0ODcgMTkuMjYzIDIzLjUyMTggMTkuMzYzMyAyMy40NzAyQzE5LjQ2MzcgMjMuNDE4NyAxOS41NTAzIDIzLjM0NCAxOS42MTYgMjMuMjUyM0wyNC4zMTY5IDE2LjczNDRDMjQuNDA0IDE2LjYxNjEgMjQuMzE5MSAxNi40NTA5IDI0LjE3NCAxNi40NTA5WiIgZmlsbD0iI0NFOTQyRCIvPg0KPHBhdGggZD0iTTIwIDEwQzE0LjQ3NzcgMTAgMTAgMTQuNDc3NyAxMCAyMEMxMCAyNS41MjIzIDE0LjQ3NzcgMzAgMjAgMzBDMjUuNTIyMyAzMCAzMCAyNS41MjIzIDMwIDIwQzMwIDE0LjQ3NzcgMjUuNTIyMyAxMCAyMCAxMFpNMjAgMjguMzAzNkMxNS40MTUyIDI4LjMwMzYgMTEuNjk2NCAyNC41ODQ4IDExLjY5NjQgMjBDMTEuNjk2NCAxNS40MTUyIDE1LjQxNTIgMTEuNjk2NCAyMCAxMS42OTY0QzI0LjU4NDggMTEuNjk2NCAyOC4zMDM2IDE1LjQxNTIgMjguMzAzNiAyMEMyOC4zMDM2IDI0LjU4NDggMjQuNTg0OCAyOC4zMDM2IDIwIDI4LjMwMzZaIiBmaWxsPSIjQ0U5NDJEIi8+DQo8L3N2Zz4NCg==',
    color: '#CE942D',
  },
  danger: {
    checkSvg:
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCjxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjIwIiBmaWxsPSIjRDMyRjJGIiBmaWxsLW9wYWNpdHk9IjAuMSIvPg0KPHBhdGggZD0iTTI0LjE3NDEgMTYuNDUwOUgyMy4xMjcyQzIyLjg5OTUgMTYuNDUwOSAyMi42ODMgMTYuNTYwMyAyMi41NDkxIDE2Ljc0NzhMMTkuMDQwMiAyMS42MTM5TDE3LjQ1MDkgMTkuNDA4NUMxNy4zMTcgMTkuMjIzMiAxNy4xMDI3IDE5LjExMTYgMTYuODcyOCAxOS4xMTE2SDE1LjgyNTlDMTUuNjgwOCAxOS4xMTE2IDE1LjU5NiAxOS4yNzY4IDE1LjY4MDggMTkuMzk1MUwxOC40NjIgMjMuMjUyM0MxOC41Mjc4IDIzLjM0NCAxOC42MTQ0IDIzLjQxODcgMTguNzE0NyAyMy40NzAyQzE4LjgxNTEgMjMuNTIxOCAxOC45MjYyIDIzLjU0ODcgMTkuMDM5MSAyMy41NDg3QzE5LjE1MTkgMjMuNTQ4NyAxOS4yNjMxIDIzLjUyMTggMTkuMzYzNCAyMy40NzAyQzE5LjQ2MzcgMjMuNDE4NyAxOS41NTA0IDIzLjM0NCAxOS42MTYxIDIzLjI1MjNMMjQuMzE3IDE2LjczNDRDMjQuNDA0IDE2LjYxNjEgMjQuMzE5MiAxNi40NTA5IDI0LjE3NDEgMTYuNDUwOVoiIGZpbGw9IiNEMzJGMkYiLz4NCjxwYXRoIGQ9Ik0yMCAxMEMxNC40Nzc3IDEwIDEwIDE0LjQ3NzcgMTAgMjBDMTAgMjUuNTIyMyAxNC40Nzc3IDMwIDIwIDMwQzI1LjUyMjMgMzAgMzAgMjUuNTIyMyAzMCAyMEMzMCAxNC40Nzc3IDI1LjUyMjMgMTAgMjAgMTBaTTIwIDI4LjMwMzZDMTUuNDE1MiAyOC4zMDM2IDExLjY5NjQgMjQuNTg0OCAxMS42OTY0IDIwQzExLjY5NjQgMTUuNDE1MiAxNS40MTUyIDExLjY5NjQgMjAgMTEuNjk2NEMyNC41ODQ4IDExLjY5NjQgMjguMzAzNiAxNS40MTUyIDI4LjMwMzYgMjBDMjguMzAzNiAyNC41ODQ4IDI0LjU4NDggMjguMzAzNiAyMCAyOC4zMDM2WiIgZmlsbD0iI0QzMkYyRiIvPg0KPC9zdmc+DQo=',
    color: '#D32F2F',
  },
}

export function PercentChart({
  type,
  style,
  title,
  percent,
}: {
  type: 'warning' | 'danger'
  title: string
  percent: number
  style?: CSSProperties
}) {
  return (
    <ReactECharts
      style={style}
      option={{
        graphic: {
          elements: [
            {
              type: 'image',
              z: 999,
              style: {
                image: typeOptions[type].checkSvg,
                width: 40,
                height: 40,
              },
              left: 'center',
              top: '24%',
            },
          ],
        },
        series: [
          {
            radius: '90%',
            type: 'gauge',
            startAngle: 204,
            endAngle: -24,
            min: 0,
            max: 100,
            itemStyle: {
              color: typeOptions[type].color,
            },
            progress: {
              show: true,
              roundCap: true,
              width: 16,
            },
            pointer: {
              show: false,
            },
            axisLine: {
              roundCap: true,
              lineStyle: {
                width: 16,
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: '#999999',
              distance: -60,
              padding: [0, 26, 0, 40],
              formatter: (value: number) => {
                if (value === 0 || value === 100) {
                  return `${value}%`
                }
              },
            },
            title: {
              show: false,
            },
            detail: {
              offsetCenter: [0, '16%'],
              valueAnimation: true,
              formatter: function (value: number) {
                if (Number(value) > 0 && Number(value < 1)) {
                  return `{a|< 1%}\n{b|${title}}`
                }
                return `{a|${Math.round(Number(value))}%}\n{b|${title}}`
              },
              rich: {
                a: {
                  fontSize: 18,
                  lineHeight: 34,
                  fontWeight: 'bolder',
                  color: '#333333',
                  padding: [0, 0, -20, 0],
                },
                b: {
                  fontSize: 14,
                  color: '#999999',
                },
              },
            },
            data: [
              {
                value: [percent],
              },
            ],
          },
        ],
      }}
    />
  )
}
