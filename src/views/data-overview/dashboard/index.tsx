import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { DatePicker, message, Spin } from 'antd'
import { OrgChat } from './components/OrgChart'
import { PercentChart } from './components/PercentChart'
import { cn } from '~/lib/utils'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

export interface Org {
  id: string
  name: string
  parent_id?: string
  children?: Org[]
}

interface OverviewData {
  develop: {
    meetingCount: number
    studyPlanCount: number
    rate: number
  }
  discuss: Array<{
    finish: number
    rate: number
  }>
  vacation: {
    total: number
    threeTimes: number
  }
  issue: {
    total: number
  }
  report: {
    total: number
  }
}

const DataOverviewDashboard = () => {
  const { userInfo } = useAuthStore()
  const [loading, setLoading] = useState(true)
  const [overviewData, setOverviewData] = useState<OverviewData>()
  const [currentDiscussIndex, setCurrentDiscussIndex] = useState(0)
  const [year, setYear] = useState(dayjs())

  const [orgs, setOrgs] = useState<Org[]>([])
  const [currentCompanyId, setCurrentCompanyId] = useState<string | undefined>(
    userInfo?.company_id
  )

  const fetchOrgs = async (id?: string) => {
    if (!id) return

    try {
      const response = await service.get<ApiResponse<Org[]>>('/orgs/son', {
        params: {
          id,
        },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return
      }

      const filterOrgs = response.data?.filter(
        (item) => item.id !== 'f3ce04b6-e06e-408f-b350-249a3da2ac1e'
      )

      setOrgs(filterOrgs)
    } catch (error) {
      message.error('获取组织列表失败')
      console.log(error)
    }
  }

  useEffect(() => {
    const getOverviewData = async () => {
      setLoading(true)

      try {
        const response = await service.get<ApiResponse<OverviewData>>(
          '/dataPanorama/home',
          {
            params: {
              year: year.format('YYYY'),
              company_id: currentCompanyId,
            },
          }
        )

        if (response.code === 200001) {
          setOverviewData(response.data)
        } else {
          message.error(response.message)
        }
      } finally {
        setLoading(false)
      }
    }
    getOverviewData()
  }, [currentCompanyId, year])

  useEffect(() => {
    fetchOrgs(currentCompanyId)
  }, [currentCompanyId])

  // 用户是否为“保利华信”组织
  const isBLHXUser =
    userInfo?.company_id === '71d028da-4a3b-4d18-8d04-e095f00b13e5'
  // 是否为保利华信
  const isBLHX = (item: Org) =>
    item.id !== '71d028da-4a3b-4d18-8d04-e095f00b13e5'

  return (
    <Spin spinning={loading}>
      <div className="flex h-full flex-col gap-8 overflow-y-auto bg-white p-4">
        <div className="flex flex-col gap-2">
          <div className="flex items-center">
            <p className="flex-1 text-2xl font-semibold text-[#333333]">
              数屏全景
            </p>
            <div className="flex-shrink-0 flex-grow-0">
              <DatePicker value={year} onChange={setYear} picker="year" />
            </div>
          </div>

          <p className="text-base">{dayjs().format('YYYY')}年度</p>
        </div>
        {!isBLHXUser && (
          <div className="flex flex-col gap-2">
            <p className="text-xl font-semibold text-[#333333]">党组织层级</p>
            <OrgChat
              data={(orgs || []).filter(isBLHX)}
              onSelect={(value) => setCurrentCompanyId(value)}
              current={currentCompanyId}
            />
          </div>
        )}
        <div className="grid sm:grid-cols-2 xl:grid-cols-4">
          <div className="flex flex-col gap-4 border-r border-r-[#E9E9E9] p-4">
            <h4 className="text-xl font-semibold text-[#333333]">开展情况</h4>
            <div className="h-[200px]">
              <PercentChart
                type="danger"
                title="开展率"
                style={{ height: '100%' }}
                percent={overviewData?.develop.rate ?? 0}
              />
            </div>
            <div className="-mt-7 flex min-h-24 flex-col items-center justify-center gap-1 rounded-lg bg-[#D32F2F1A] p-4">
              <div className="flex items-end gap-2 text-base font-semibold text-[#333333]">
                实际开展
                <span className="text-3xl">
                  {overviewData?.develop.meetingCount}
                </span>
                次
              </div>
              <div className="text-sm text-[#D32F2F]">
                计划完成 {overviewData?.develop.studyPlanCount} 次中心组学习
              </div>
            </div>
          </div>
          <div className="relative flex flex-col gap-4 p-4 sm:border-r-0 xl:border-r">
            <div className="absolute right-1 top-4 z-50">
              <div className="flex overflow-hidden rounded-lg">
                {['一', '二', '三', '四', '全年'].map((item, index) => {
                  return (
                    <div
                      key={item}
                      className={cn(
                        'cursor-pointer bg-[#CE942D1A] p-2 text-xs text-[#CE942D]',
                        {
                          'bg-[#CE942D] text-white':
                            index === currentDiscussIndex,
                        }
                      )}
                      onClick={() => setCurrentDiscussIndex(index)}
                    >
                      {item}
                    </div>
                  )
                })}
              </div>
            </div>
            <h4 className="text-xl font-semibold text-[#333333]">研讨情况</h4>
            <div className="h-[200px]">
              <PercentChart
                type="warning"
                title="研讨占比"
                style={{ height: '100%' }}
                percent={
                  overviewData?.discuss?.[currentDiscussIndex]?.rate ?? 0
                }
              />
            </div>
            <div className="-mt-7 flex min-h-24 flex-col items-center justify-center gap-1 rounded-lg bg-[#CE942D1A] p-4">
              <div className="flex items-end gap-2 text-base font-semibold text-[#333333]">
                开展研讨
                <span className="text-3xl">
                  {overviewData?.discuss[currentDiscussIndex].finish}
                </span>
                次
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-6 p-4">
            <h4 className="flex-shrink-0 flex-grow-0 text-xl font-semibold text-[#333333]">
              请假情况
            </h4>
            <div className="flex flex-1 flex-shrink-0 flex-col">
              <div className="flex h-full min-h-32 flex-col items-center justify-center gap-4 bg-[#CE942DCC] text-white">
                <div className="text-lg">全年请假总次数</div>
                <div className="text-4xl font-semibold">
                  {overviewData?.vacation.total}
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-col">
              <div
                className={cn(
                  'relative flex h-full min-h-32 flex-col items-center justify-center gap-4 bg-[#CE942D1A] text-[#CE942D]'
                )}
              >
                <div className="text-lg">累计请假超3次人数</div>
                <div className="text-4xl font-semibold">
                  {overviewData?.vacation.threeTimes}
                </div>
                {/* Top left and right borders */}
                <div className="absolute left-0 top-0 h-8 w-8 border-l-2 border-t-2 border-[#CE942D]" />
                <div className="absolute right-0 top-0 h-8 w-8 border-r-2 border-t-2 border-[#CE942D]" />

                {/* Bottom left and right borders */}
                <div className="absolute bottom-0 left-0 h-8 w-8 border-b-2 border-l-2 border-[#CE942D]" />
                <div className="absolute bottom-0 right-0 h-8 w-8 border-b-2 border-r-2 border-[#CE942D]" />
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-6 p-4">
            <h4 className="flex-shrink-0 flex-grow-0 text-xl font-semibold text-[#333333]">
              发刊及调研情况
            </h4>
            <div className="flex flex-1 flex-col">
              <div
                className={cn(
                  'relative flex h-full min-h-32 flex-col items-center justify-center gap-4 bg-[#202556] text-white'
                )}
              >
                <div className="text-lg">发表理论文章数</div>
                <div className="text-4xl font-semibold">
                  {overviewData?.issue.total}
                </div>
                {/* Top left and right borders */}
                <div className="absolute left-0 top-0 h-8 w-8 border-l-2 border-t-2 border-[#202556]" />
                <div className="absolute right-0 top-0 h-8 w-8 border-r-2 border-t-2 border-[#202556]" />

                {/* Bottom left and right borders */}
                <div className="absolute bottom-0 left-0 h-8 w-8 border-b-2 border-l-2 border-[#202556]" />
                <div className="absolute bottom-0 right-0 h-8 w-8 border-b-2 border-r-2 border-[#202556]" />
              </div>
            </div>
            <div className="flex flex-1 flex-col">
              <div
                className={cn(
                  'relative flex h-full min-h-32 flex-col items-center justify-center gap-4 bg-[#2025561A] text-[#202556]'
                )}
              >
                <div className="text-lg">专题调研数量</div>
                <div className="text-4xl font-semibold">
                  {overviewData?.report.total}
                </div>
                {/* Top left and right borders */}
                <div className="absolute left-0 top-0 h-8 w-8 border-l-2 border-t-2 border-[#202556]" />
                <div className="absolute right-0 top-0 h-8 w-8 border-r-2 border-t-2 border-[#202556]" />

                {/* Bottom left and right borders */}
                <div className="absolute bottom-0 left-0 h-8 w-8 border-b-2 border-l-2 border-[#202556]" />
                <div className="absolute bottom-0 right-0 h-8 w-8 border-b-2 border-r-2 border-[#202556]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  )
}

export default DataOverviewDashboard
