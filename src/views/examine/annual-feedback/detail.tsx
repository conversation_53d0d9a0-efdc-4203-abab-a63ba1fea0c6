import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate, useParams } from 'react-router-dom'
import { useCallback, useEffect, useState } from 'react'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'

const AnnualFeedbackDetail: React.FC = () => {
  const [tableData, setTableData] = useState<any[]>([])
  const [pager, setPager] = useState<any>({
    current: 1,
    pageSize: 10,
  })
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const navigate = useNavigate()

  // 获取路由参数
  const { dirId, year } = useParams<{ year: string; dirId: string }>()

  const getData = useCallback(async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/evaluation/yearFeedback/data', {
        params: {
          current: pager.current,
          pageSize: pager.pageSize,
          dir_id: dirId,
        },
      })
      if (res.code !== 200001) return
      setTotal(res.data.pager.total || 0)
      setTableData(res.data.list || [])
    } finally {
      setLoading(false)
    }
  }, [dirId, pager])

  useEffect(() => {
    getData()
  }, [getData])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '党组织名称',
      dataIndex: 'org_name',
      key: 'org_name',
      width: 170,
      align: 'left',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'left',
    },
    {
      title: '最后修改人',
      dataIndex: 'upload_user_name',
      key: 'upload_user_name',
      width: 120,
      align: 'left',
    },
    {
      title: '最后编辑时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <Flex wrap justify="center">
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() =>
              navigate(
                `/examine/annual-feedback/${dirId}/${year}/${record?.org_short_name}/${record.id}/detail/1`
              )
            }
          >
            查看详情
          </Button>
          {record?.operations?.update && (
            <Button
              type="link"
              className="primary-btn-link"
              onClick={() =>
                navigate(
                  `/examine/annual-feedback/${dirId}/${year}/${record?.org_short_name}/${record.id}/detail/2`
                )
              }
            >
              编辑
            </Button>
          )}
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价' },
          { title: '年度反馈', href: '/examine/annual-feedback' },
          { title: `${year}年` },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={total}
          onChange={(page, pageSize) => {
            setPager({
              current: page,
              pageSize,
            })
          }}
        ></CustomTable>
      </div>
    </div>
  )
}

export default AnnualFeedbackDetail
