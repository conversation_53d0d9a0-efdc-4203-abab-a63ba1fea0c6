import { Button, Form, Breadcrumb, Flex, Input, Spin, message } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { ApiResponse, service } from '~/lib/service'
import { getMeetingName } from '~/lib/utils'

const AnnualFeedbackEdit: React.FC = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const {
    id,
    dirId,
    type: routeType,
    year,
    orgShortName,
  } = useParams<{
    id: string
    type: string
    year: string
    dirId: string
    orgShortName: string
  }>()
  const [loading, setLoading] = useState(false)
  const [detail, setDetail] = useState<any>({})
  const [qualityEvaluation, setQualityEvaluation] = useState<any>({})
  const [submiting, setSubmiting] = useState(false)

  const isEdit = routeType === '2'

  const getDetail = useCallback(async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<any>>(
        '/evaluation/yearFeedback/data/detail',
        {
          params: {
            id,
          },
        }
      )
      if (res.code !== 200001) return
      setDetail(res.data)
      setQualityEvaluation(res.data?.base_info?.quality_evaluation ?? {})
    } finally {
      setLoading(false)
    }
  }, [id])

  useEffect(() => {
    getDetail()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    form.setFieldsValue(qualityEvaluation)
  }, [qualityEvaluation, form])

  const handleSubmit = async (values: any) => {
    setSubmiting(true)
    try {
      const res = await service.put<ApiResponse<unknown>>(
        '/evaluation/yearFeedback/data',
        {
          id,
          quality_evaluation: values,
        }
      )
      if (res.code !== 200001) return message.warning(res.message)
      message.success('保存成功')
    } finally {
      setSubmiting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex h-full flex-col items-center justify-center bg-white">
        <Spin />
      </div>
    )
  }

  const quantity_evaluation = detail?.base_info?.quantity_evaluation ?? {}

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价' },
          { title: '年度反馈', href: '/examine/annual-feedback' },
          { title: `${year}年` },
        ]}
      />

      <div className="mt-4 flex h-0 w-full flex-1 flex-col items-center bg-white">
        <p className="w-full border-b border-gray-200 p-4 font-semibold">
          {year}年{orgShortName}开展情况考核评价表
        </p>
        <div className="h-0 w-full flex-1 overflow-auto overflow-x-hidden p-4">
          <Form
            form={form}
            onFinish={handleSubmit}
            style={{ width: '100%', padding: '20px 40px' }}
          >
            <div style={{ width: '100%' }}>
              <p
                style={{
                  width: '100%',
                  margin: '20px 20px 20px 0',
                  textAlign: 'left',
                  fontWeight: 'bold',
                  fontSize: '16px',
                }}
              >
                量化考核
              </p>
            </div>
            <table
              style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: '14px',
              }}
            >
              <tbody>
                <tr>
                  <td rowSpan={4} className="table-one-td">
                    材料是否完备
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">1、全年学习计划</div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {quantity_evaluation?.['全年学习计划']}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">2、全年学习台账</div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      <div>
                        开展学习总次数:
                        {
                          quantity_evaluation?.['全年学习台账']?.[
                            '开展学习总次数'
                          ]
                        }
                      </div>
                      <div>
                        计划内学习次数:
                        {
                          quantity_evaluation?.['全年学习台账']?.[
                            '计划内学习次数'
                          ]
                        }
                      </div>
                      <div>
                        集体研讨次数:
                        {
                          quantity_evaluation?.['全年学习台账']?.[
                            '集体研讨次数'
                          ]
                        }
                      </div>
                      <div>
                        集体研讨占比:
                        {quantity_evaluation?.['全年学习台账']?.[
                          '集体研讨占比'
                        ] + '%'}
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">3、全年学习报告</div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {quantity_evaluation?.['全年学习报告']}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">
                      4、历次中心组学习的全部材料(学习安排、学习议题、学习简报、发言材料、主持词)
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      <div>
                        {
                          quantity_evaluation?.[
                            '历次中心组学习的全部材料(学习安排、学习议题、学习简报、发言材料、主持词)'
                          ]?.text
                        }
                      </div>
                      {(
                        quantity_evaluation?.[
                          '历次中心组学习的全部材料(学习安排、学习议题、学习简报、发言材料、主持词)'
                        ]?.file_empty_meeting || []
                      ).map((item: { id: string; name: string }) => {
                        return (
                          <div
                            style={{
                              color: 'red',
                              textDecoration: 'underline',
                            }}
                            className="cursor-pointer"
                            key={item.id}
                            onClick={() =>
                              navigate(`/group/meeting/${item.id}`)
                            }
                          >
                            {getMeetingName(item)}
                          </div>
                        )
                      })}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td rowSpan={4} className="table-one-td">
                    组织是否完善
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      1、全年计划外学习数量是否超过累计学习数量的三分之一
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      <div>
                        {
                          quantity_evaluation?.[
                            '全年计划外学习数量是否超过累计学习数量的三分之一'
                          ]?.text
                        }
                      </div>
                      <div>
                        计划外学习数量:
                        {
                          quantity_evaluation?.[
                            '全年计划外学习数量是否超过累计学习数量的三分之一'
                          ]?.['计划外学习数量']
                        }
                        ，累计学习数量
                        {
                          quantity_evaluation?.[
                            '全年计划外学习数量是否超过累计学习数量的三分之一'
                          ]?.['累计学习数量']
                        }
                        ，占比:
                        {
                          quantity_evaluation?.[
                            '全年计划外学习数量是否超过累计学习数量的三分之一'
                          ]?.['占比']
                        }
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">
                      2、是否每次安排至少3位中心组成员交流发言
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {
                        quantity_evaluation?.[
                          '是否每次安排至少3位中心组成员交流发言'
                        ]
                      }
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">
                      3、每位中心组成员全年平均发言次数是否少于3次
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {
                        quantity_evaluation?.[
                          '每位中心组成员全年平均发言次数是否少于3次'
                        ]?.['text']
                      }
                    </div>
                    {(
                      quantity_evaluation?.[
                        '每位中心组成员全年平均发言次数是否少于3次'
                      ]?.count_res || []
                    ).map((item: { count: number; name: string }) => {
                      return (
                        <div key={item.name} className="text-[#212a7c]">
                          {item.name}:{item.count}
                        </div>
                      )
                    })}
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">
                      4、全年是否安排全员交流发言的集体研讨
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      <div>
                        {
                          quantity_evaluation?.[
                            '全年是否安排全员交流发言的集体研讨'
                          ]?.['text']
                        }
                      </div>
                      {(
                        quantity_evaluation?.[
                          '全年是否安排全员交流发言的集体研讨'
                        ]?.meetings || []
                      ).map((item: { id: string; name: string }) => {
                        return (
                          <div
                            style={{
                              color: 'red',
                              textDecoration: 'underline',
                            }}
                            className="cursor-pointer"
                            key={item.id}
                            onClick={() =>
                              navigate(`/group/meeting/${item.id}`)
                            }
                          >
                            {getMeetingName(item)}
                          </div>
                        )
                      })}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-one-td">管理是否规范</td>
                  <td className="table-td">
                    <div className="table-td-div">
                      1、是否履行请假补学流程并完成所有补学材料
                    </div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      <div>
                        {
                          quantity_evaluation?.[
                            '是否履行请假补学流程并完成所有补学材料'
                          ]?.['text']
                        }
                      </div>
                      <div>
                        会议最多请假次数：
                        {
                          quantity_evaluation?.[
                            '是否履行请假补学流程并完成所有补学材料'
                          ]?.['max_leave_count']
                        }
                      </div>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td rowSpan={2} className="table-one-td">
                    成果转化是否见效
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">1、全年发布理论文章篇数</div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {quantity_evaluation?.['全年发布理论文章篇数']}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td className="table-td">
                    <div className="table-td-div">2、全年形成调研报告篇数</div>
                  </td>
                  <td className="table-td">
                    <div className="table-td-div">
                      {quantity_evaluation?.['全年形成调研报告篇数']}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div style={{ width: '100%' }}>
              <p
                style={{
                  width: '100%',
                  margin: '20px 20px 20px 0',
                  textAlign: 'left',
                  fontWeight: 'bold',
                  fontSize: '16px',
                }}
              >
                质化考核
              </p>
            </div>
            <table
              style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: '14px',
              }}
            >
              <tbody>
                <tr>
                  <td className="table-one-td">是否坚持以政治学习为本位</td>
                  <td className="table-td">
                    <div className="table-td-div">
                      认真学习认真领会、坚决贯彻落实党中央决策部署和习近平总书记重要指示批示精神，不断提高政治判断力、政治领悟力、政治执行力，自觉在思想上政治上行动上同以习近平同志为核心的党中央保持高度一致。
                    </div>
                  </td>
                  <td className="table-td">
                    <Form.Item
                      name="是否坚持以政治学习为本位"
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <Input.TextArea
                        placeholder="请输入"
                        disabled={!isEdit}
                      ></Input.TextArea>
                    </Form.Item>
                  </td>
                </tr>
                <tr>
                  <td className="table-one-td">是否坚持创新学习形式</td>
                  <td className="table-td">
                    <div className="table-td-div">
                      通过专题讲座、读书班、多级联学、实地践学等方式，增强学习的吸引力、针对性和实效性。
                    </div>
                  </td>
                  <td className="table-td">
                    <Form.Item
                      name="是否坚持创新学习形式"
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <Input.TextArea
                        placeholder="请输入"
                        disabled={!isEdit}
                      ></Input.TextArea>
                    </Form.Item>
                  </td>
                </tr>
                <tr>
                  <td className="table-one-td">是否开展对下替导工作</td>
                  <td className="table-td">
                    <div className="table-td-div">
                      通过列席旁听、座谈调研等方式，加强对子公司中心组学习的指导帮助
                    </div>
                  </td>
                  <td className="table-td">
                    <Form.Item
                      name="是否开展对下替导工作"
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <Input.TextArea
                        placeholder="请输入"
                        disabled={!isEdit}
                      ></Input.TextArea>
                    </Form.Item>
                  </td>
                </tr>
                <tr>
                  <td className="table-one-td">其他需要反馈的工作评价</td>
                  <td className="table-td">
                    <div className="table-td-div"></div>
                  </td>
                  <td className="table-td">
                    <Form.Item
                      name="其他需要反馈的工作评价"
                      rules={[{ required: true, message: '请输入' }]}
                    >
                      <Input.TextArea
                        placeholder="请输入"
                        disabled={!isEdit}
                      ></Input.TextArea>
                    </Form.Item>
                  </td>
                </tr>
              </tbody>
            </table>
          </Form>
        </div>
        <Flex justify="end" gap="small" className="w-full p-4">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() =>
              navigate(`/examine/annual-feedback/${dirId}/${year}/list`)
            }
          >
            返回
          </Button>
          {isEdit && (
            <Button
              loading={submiting}
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={() => form.submit()}
            >
              保存
            </Button>
          )}
        </Flex>
      </div>

      <style lang="lass">
        {`
          .table-one-td{
            width: 30%;
            minHeight: 20px;
            border: 1px solid #e9e9e9;
            text-align: center;
            vertical-align: middle;
            background-color:#fdf5f5;
          }
          .table-td{
            width: 40%;
            border: 1px solid #e9e9e9;
            text-align: left;
            vertical-align: middle;
            padding:10px 10px;
          }
          .table-td-div{
          minHeight: 60px;
          lin-height:60px;
          }
          `}
      </style>
    </div>
  )
}

export default AnnualFeedbackEdit
