import { ExclamationCircleFilled } from '@ant-design/icons'
import { <PERSON><PERSON><PERSON>rumb, <PERSON><PERSON>, DatePicker, Form, Modal, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useLocation, useParams } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

const AnnualReportDetail: React.FC = () => {
  const [tableData, setTableData] = useState<any[]>([])
  const [pager, setPager] = useState<any>({
    current: 1,
    pageSize: 10,
  })
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const fileInput = useRef<HTMLInputElement>(null)
  const [uploadId, setUploadId] = useState('')
  const [messageApi, contextHolder] = message.useMessage()
  const { id } = useParams()

  const location = useLocation()
  const { year } = location.state

  const [showEndDateDialog, setShowEndDateDialog] = useState(false)
  const [endDate, setEndDate] = useState('')
  const [currentEditData, setCurrentEditData] = useState<any>({})

  const { userInfo } = useAuthStore()

  const getData = useCallback(async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/evaluation/yearReport/data', {
        params: {
          current: pager.current,
          pageSize: pager.pageSize,
          dir_id: id,
        },
      })
      if (res.code !== 200001) return
      setTotal(res.data.pager.total || 0)
      setTableData(res.data.list || [])
    } finally {
      setLoading(false)
    }
  }, [id, pager])

  const handleFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `/files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const handleFileDownload = async (fileId: string, name: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    const fileName = decodeURIComponent((name || '').split('/').pop() || 'file')
    try {
      const res = await service.get<Blob>(`/files/${fileId}/download`, {
        responseType: 'blob',
      })
      const blobUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      message.destroy()
      message.success('文件下载成功')
    } catch (error: any) {
      message.destroy()
      message.open({
        type: 'error',
        content: error?.message,
        duration: 2,
      })
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    messageApi.open({
      key: 'upload',
      type: 'loading',
      content: '正在上传...',
    })
    try {
      const fileRes = await service.post<
        ApiResponse<{ id: string; name: string; url: string }>
      >(
        '/files',
        { file },
        { headers: { 'Content-Type': 'multipart/form-data' } }
      )
      if (fileRes.code !== 200001) {
        messageApi.open({
          key: 'upload',
          type: 'error',
          content: fileRes.message,
        })
        return
      }
      const savedRes = await service.put<ApiResponse<unknown>>(
        '/evaluation/yearReport/file',
        {
          id: uploadId,
          files: [fileRes.data],
        }
      )
      if (savedRes.code !== 200001) {
        messageApi.open({
          key: 'upload',
          type: 'error',
          content: savedRes.message,
        })
        return
      }
      messageApi.open({
        key: 'upload',
        type: 'success',
        content: '文件上传成功',
      })
      getData()
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      messageApi.open({
        key: 'upload',
        type: 'error',
        content: '上传失败',
      })
    } finally {
      if (fileInput.current) {
        fileInput.current.value = ''
      }
    }
  }

  useEffect(() => {
    getData()
  }, [getData])

  const { confirm } = Modal

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '党组织名称',
      dataIndex: 'org_short_name',
      key: 'org_short_name',
      width: 170,
      align: 'left',
    },
    {
      title: '年度报告',
      dataIndex: 'report_file',
      key: 'report_file',
      width: 350,
      align: 'left',
      render: (_, record: any) => {
        const { id, url, name } = record?.report_file?.[0] || {}
        if (!url) return null
        return (
          <Button
            className="p-0"
            type="link"
            onClick={() => handleFilePreview(id)}
            style={{ maxWidth: '300px', whiteSpace: 'wrap' }}
          >
            {name || url}
          </Button>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'left',
    },
    {
      title: '上传人',
      dataIndex: 'upload_user_name',
      key: 'upload_user_name',
      width: 150,
      align: 'left',
    },
    {
      title: '上传时间',
      dataIndex: 'upload_time',
      key: 'upload_time',
      width: 200,
      align: 'left',
    },
    {
      title: '材料提交截至时间',
      dataIndex: 'end_at',
      key: 'end_at',
      width: 200,
      align: 'left',
      render: (text) => {
        if (!text) return null
        return dayjs(text).format('YYYY-MM-DD')
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 260,
      align: 'center',
      render: (_, record: any) => (
        <div className="flex" style={{ flexWrap: 'wrap' }}>
          {userInfo?.company_id !== record?.organization_id && (
            <Button
              type="link"
              size="large"
              style={{ textDecoration: 'underline' }}
              onClick={() => {
                setCurrentEditData(record)
                setShowEndDateDialog(true)
              }}
            >
              设置提交时限
            </Button>
          )}

          {record?.operations?.upload && (
            <Button
              type="link"
              size="large"
              style={{ textDecoration: 'underline' }}
              className="primary-btn-link"
              onClick={() => {
                if (!fileInput.current) return
                setUploadId(record?.id)
                fileInput.current.click()
              }}
            >
              上传报告
            </Button>
          )}
          {record?.operations?.download && (
            <Button
              type="link"
              size="large"
              style={{ textDecoration: 'underline' }}
              className="primary-btn-link"
              onClick={() => {
                handleFileDownload(
                  record?.report_file?.[0]?.id,
                  record?.report_file?.[0]?.name
                )
              }}
            >
              下载
            </Button>
          )}
          {record?.operations?.reject && (
            <Button
              type="link"
              size="large"
              className="danger-btn-link"
              style={{ textDecoration: 'underline' }}
              onClick={() => handleReject(record?.id)}
            >
              驳回
            </Button>
          )}
          {record.end_at && (
            <Button
              type="link"
              size="large"
              className="danger-btn-link"
              style={{ textDecoration: 'underline' }}
              onClick={() => handleRemind(record)}
              disabled={!!record?.msg_at}
            >
              {record.msg_at ? '已提醒' : '提醒'}
            </Button>
          )}
        </div>
      ),
    },
  ] as ColumnsType<DataType>
  const handleReject = (id: string) => {
    confirm({
      title: '驳回提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认驳回该条数据?',
      centered: true,
      async onOk() {
        const res = await service.put<ApiResponse<unknown>>(
          '/evaluation/yearReport/reject',
          {
            id,
          }
        )
        if (res.code !== 200001) return message.warning(res.message)
        message.success('驳回成功')
        getData()
      },
    })
  }

  const handleRemind = (record: any) => {
    if (record.end_at && dayjs(record.end_at).isBefore(dayjs())) {
      const dialog = confirm({
        title: '提醒提示',
        icon: <ExclamationCircleFilled />,
        content: '当前已超过提交时限，是否要更新提交时限?',
        centered: true,
        onOk() {
          dialog.destroy()
          setCurrentEditData(record)
          setShowEndDateDialog(true)
        },
        async onCancel() {
          const res = await service.put<ApiResponse<unknown>>(
            `/evaluation/yearReport/data/msg/${record.id}`
          )
          if (res.code !== 200001) return message.warning(res.message)
          message.success('提醒成功')
          getData()
        },
        okText: '修改',
        cancelText: '不修改',
        footer: (_, { CancelBtn, OkBtn }) => {
          return (
            <>
              <OkBtn />
              <CancelBtn />
              <Button
                onClick={() => {
                  dialog.destroy()
                }}
              >
                取消
              </Button>
            </>
          )
        },
      })
      return dialog
    }
    confirm({
      title: '提醒提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认提醒该条数据?',
      centered: true,
      async onOk() {
        const res = await service.put<ApiResponse<unknown>>(
          `/evaluation/yearReport/data/msg/${record.id}`
        )
        if (res.code !== 200001) return message.warning(res.message)
        message.success('提醒成功')
        getData()
      },
    })
  }

  const handleUpdateEndDate = async () => {
    setLoading(true)
    try {
      const res = await service.put<ApiResponse<unknown>>(
        `/evaluation/yearReport/data/${currentEditData.id}`,
        {
          end_at: dayjs(endDate).format('YYYY-MM-DD HH:mm:ss'),
        }
      )
      if (res.code !== 200001) return message.warning(res.message)
      message.success('更新成功')
      getData()
    } finally {
      setShowEndDateDialog(false)
      setLoading(false)
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价' },
          { title: '年度报告', href: '/examine/annual-report' },
          { title: year + '年' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={total}
          onChange={(page, pageSize) => {
            setPager({
              current: page,
              pageSize,
            })
          }}
        ></CustomTable>
      </div>
      <input
        type="file"
        className="hidden"
        ref={fileInput}
        accept=".doc, .docx, .xls, .pdf, .ppt,pptx, .png, .zip, .tar"
        onChange={handleFileChange}
      />
      {contextHolder}

      <Modal
        title="更新提交时间"
        open={showEndDateDialog}
        centered
        destroyOnClose
        okButtonProps={{
          loading: loading,
        }}
        onOk={handleUpdateEndDate}
        onCancel={() => {
          setShowEndDateDialog(false)
          setEndDate('')
          setCurrentEditData({})
        }}
      >
        <Form clearOnDestroy autoComplete="off" labelAlign="left">
          <Form.Item
            label="更新提交时限"
            rules={[{ required: true, message: '请选择时间' }]}
          >
            <DatePicker
              value={endDate}
              format="YYYY-MM-DD"
              className="w-full"
              placeholder="请选择年份"
              onChange={setEndDate}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default AnnualReportDetail
