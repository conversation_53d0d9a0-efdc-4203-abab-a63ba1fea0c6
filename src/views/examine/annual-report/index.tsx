import { ExclamationCircleFilled } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Flex,
  Modal,
  message,
  DatePicker,
  Form,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { useCallback, useEffect, useState } from 'react'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

const AnnualReport: React.FC = () => {
  const [createForm] = Form.useForm()
  const [tableData, setTableData] = useState<any[]>([])
  const [pager, setPager] = useState<any>({
    current: 1,
    pageSize: 10,
  })
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [submiting, setSubmiting] = useState(false)
  const { confirm } = Modal
  const navigate = useNavigate()
  const { userInfo } = useAuthStore()

  const [deadlineModalVisible, setDeadlineModalVisible] = useState(false)

  const getData = useCallback(async () => {
    setLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/evaluation/yearReport/dir', {
        params: {
          current: pager.current,
          pageSize: pager.pageSize,
        },
      })
      if (res.code !== 200001) return
      setTotal(res.data.pager.total || 0)
      setTableData(res.data.list || [])
    } finally {
      setLoading(false)
    }
  }, [pager])

  useEffect(() => {
    getData()
  }, [getData])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      width: 170,
      align: 'left',
    },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      align: 'left',
    },
    {
      title: '最后修改时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <Flex wrap justify="center">
          <Button
            type="link"
            className="primary-btn-link"
            onClick={() =>
              navigate(`/examine/annual-report/${record.id}/detail`, {
                state: { year: record.year },
              })
            }
          >
            查看详情
          </Button>

          {record.operations.delete && (
            <Button
              type="link"
              className="danger-btn-link"
              onClick={() => handleRowDelete(record)}
            >
              删除
            </Button>
          )}
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  const handleRowDelete = (row: any) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      async onOk() {
        const res = await service.delete<ApiResponse<unknown>>(
          '/evaluation/yearReport/dir',
          {
            data: {
              ids: [row.id],
            },
          }
        )
        if (res.code !== 200001) return message.warning(res.message)
        message.success('删除成功')
        getData()
      },
    })
  }

  const handleCreate = async () => {
    setSubmiting(true)
    try {
      const values = await createForm.validateFields()
      const res = await service.post<ApiResponse<unknown>>(
        '/evaluation/yearReport/dir',
        {
          year: dayjs(values.year).get('year'),
        }
      )
      if (res.code !== 200001) return message.warning(res.message)
      message.success('创建成功')
      setDeadlineModalVisible(false)
      getData()
    } finally {
      setSubmiting(false)
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '考核评价' }, { title: '年度报告' }]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="primary-btn"
            onClick={() => setDeadlineModalVisible(true)}
            style={{
              visibility:
                (userInfo?.roles.includes('qt_undertaker') &&
                  userInfo.company_id ===
                    '00000000-0000-0000-0000-000000000000') ||
                userInfo?.roles.includes('sa')
                  ? 'visible'
                  : 'hidden',
            }}
          >
            创建年度报告文件夹
          </Button>
        </Flex>
        <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={total}
          onChange={(page, pageSize) => {
            setPager({
              current: page,
              pageSize,
            })
          }}
        ></CustomTable>
      </div>

      <Modal
        title="创建年度报告文件夹"
        open={deadlineModalVisible}
        centered
        destroyOnClose
        okButtonProps={{
          loading: submiting,
        }}
        onOk={handleCreate}
        onCancel={() => setDeadlineModalVisible(false)}
      >
        <Form
          form={createForm}
          initialValues={{ year: '' }}
          clearOnDestroy
          autoComplete="off"
          labelAlign="left"
        >
          <Form.Item
            label="年度"
            name="year"
            rules={[{ required: true, message: '请选择年份' }]}
          >
            <DatePicker
              picker="year"
              showTime
              format="YYYY"
              style={{ width: '100%' }}
              placeholder="请选择年份"
            />
          </Form.Item>
          {/* <Form.Item
            label="材料提交截至时间"
            name="end_at"
            rules={[{ required: true, message: '请选择时间' }]}
          >
            <DatePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              placeholder="请选择年份"
            />
          </Form.Item> */}
        </Form>
      </Modal>
    </div>
  )
}

export default AnnualReport
