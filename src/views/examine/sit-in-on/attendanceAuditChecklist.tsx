import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Picker,
  Flex,
  Form,
  Input,
  message,
  Row,
  Select,
} from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import FileUpload from '~/components/upload-file'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'
import { flattenedData } from '~/utils'

const AttendanceAuditChecklist: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const { userInfo } = useAuthStore()

  const [meetingInfoForm] = Form.useForm()

  const [orgs, setOrgs] = useState<any>([])
  const [users, setUsers] = useState<any>([])
  const [loading, setLoading] = useState(false)

  const fetchOrgs = async () => {
    try {
      const response = await service.get<ApiResponse<any>>(
        '/orgs/tree?self=true'
      )
      if (response.code !== 200001) {
        message.error(response.message)
        return
      }
      setOrgs(flattenedData(response.data))
    } catch (error) {
      message.error('获取组织列表失败')
      console.log(error)
    }
  }

  const fetchUsers = async () => {
    try {
      const response = await service.get<ApiResponse<any>>('/users', {
        params: {
          company_id: userInfo?.company_id,
          company_parent: true,
        },
      })
      if (response.code !== 200001) {
        message.error(response.message)
        return
      }
      setUsers(response.data)
    } catch (error) {
      message.error('获取用户列表失败')
      console.log(error)
    }
  }

  const getResourcesWithUrls = async (resources: any[]) => {
    if (!resources || resources.length === 0) return
    const promises = resources.map(async (item: any) => {
      const response = await service.get<ApiResponse<any>>(
        `files/${item.file_id}/preview-url`
      )
      return response.data
    })
    const urls = await Promise.all(promises)
    const newResources = resources.map((item: any, index: number) => {
      return {
        ...item,
        url: urls[index],
      }
    })
    return newResources
  }

  const getMeetingDetail = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<any>>(
        '/evaluation/meetingWatch/detail',
        {
          params: { id },
        }
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return
      }

      const baseInfo = res.data.base_info
      const resources = await getResourcesWithUrls(baseInfo.resources)

      meetingInfoForm.setFieldsValue({
        ...baseInfo,
        begin_time: baseInfo.begin_time ? dayjs(baseInfo.begin_time) : null,
        end_time: baseInfo.end_time ? dayjs(baseInfo.end_time) : null,
        check_table: {
          organization_id:
            baseInfo.check_table?.organization_id ?? baseInfo.organization_id,
          watch_time: baseInfo.check_table?.watch_time
            ? dayjs(baseInfo.check_table.watch_time)
            : baseInfo.begin_time
              ? dayjs(baseInfo.begin_time)
              : null,
          meeting_name:
            baseInfo.check_table?.meeting_name ?? baseInfo.meeting_name,
          check_items: {
            制修订中心组学习制度情况: {
              指定时间: baseInfo.check_table?.check_items?.[
                '制修订中心组学习制度情况'
              ]?.['指定时间']
                ? dayjs(
                    baseInfo.check_table.check_items[
                      '制修订中心组学习制度情况'
                    ]['指定时间']
                  )
                : null,
              修订时间: baseInfo.check_table?.check_items?.[
                '制修订中心组学习制度情况'
              ]?.['修订时间']
                ? dayjs(
                    baseInfo.check_table.check_items[
                      '制修订中心组学习制度情况'
                    ]['修订时间']
                  )
                : null,
            },
          },
        },
        resources,
      })
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    fetchOrgs()
    fetchUsers()
    getMeetingDetail()
  })

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价' },
          { title: '列席旁听' },
          { title: '填写检查表' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <p className="mb-4 font-semibold">会议信息</p>
        <Form
          form={meetingInfoForm}
          name="meetingInfoForm"
          initialValues={{ selfLearningTheme: '' }}
          clearOnDestroy
          onFinish={async (values) => {
            setLoading(true)
            const submitData = {
              ...values,
              begin_time: dayjs(values.begin_time).format(
                'YYYY-MM-DD HH:mm:ss'
              ),
              end_time: dayjs(values.end_time).format('YYYY-MM-DD HH:mm:ss'),
              check_table: {
                ...values.check_table,
                watch_time: dayjs(values.check_table.watch_time).format(
                  'YYYY-MM-DD HH:mm:ss'
                ),
              },
            }
            try {
              const response = await service.put<ApiResponse<null>>(
                '/evaluation/meetingWatch',
                { id, ...submitData }
              )

              if (response.code !== 200001) {
                message.error(response.message)
              }

              navigate('/examine/sit-in-on')
            } catch (error) {
              message.error('提交失败')
              console.log(error)
            } finally {
              setLoading(false)
            }
          }}
          autoComplete="off"
          labelCol={{ span: 2 }}
          labelAlign="left"
          className="hide-scrollbar w-full flex-1 overflow-auto overflow-x-hidden"
          disabled={loading}
        >
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="会议名称"
                name="meeting_name"
                rules={[
                  { required: true, message: '请填写会议名称' },
                  { max: 30, message: '最多30个字符' },
                ]}
              >
                <Input allowClear placeholder="请填写会议名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="会议开始时间"
                labelCol={{ span: 4 }}
                name="begin_time"
                rules={[{ required: true, message: '请选择会议开始时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="会议结束时间"
                labelCol={{ span: 4 }}
                name="end_time"
                rules={[{ required: true, message: '请选择会议结束时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  width={'100%'}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="会议材料" name="resources">
                <FileUpload multiple maxCount={Infinity} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="会议地点" name="addr">
                <Input allowClear placeholder="请填写会议地点" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="被旁听单位"
                name="organization_id"
                rules={[{ required: true, message: '请填写被旁听单位' }]}
              >
                <Select
                  allowClear
                  placeholder="请填写被旁听单位"
                  showSearch
                  filterOption={(input: string, option: any) =>
                    option.label.includes(input)
                  }
                  options={orgs.map((org: any) => ({
                    value: org.id,
                    label: org.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <hr className="border-t border-dashed border-neutral-200" />
          <p className="my-4 font-semibold">列席旁听检查表</p>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass">
              <div>旁听时间</div>
            </Col>
            <Col span={8} className="colClass">
              <Form.Item
                name={['check_table', 'watch_time']}
                style={{ width: '100%', marginBottom: '0px' }}
                rules={[{ required: true, message: '请选择旁听时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  width={'100%'}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                />
              </Form.Item>
            </Col>
            <Col span={4} className="colClass">
              <div>被旁听单位</div>
            </Col>
            <Col span={8} className="colClass">
              <Form.Item
                name={['check_table', 'organization_id']}
                style={{ width: '100%', marginBottom: '0px' }}
                rules={[{ required: true, message: '请填写被旁听单位' }]}
              >
                <Select
                  allowClear
                  placeholder="请填写被旁听单位"
                  showSearch
                  filterOption={(input: string, option: any) =>
                    option.label.includes(input)
                  }
                  options={orgs.map((org: any) => ({
                    value: org.id,
                    label: org.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass">
              <div>旁听成员</div>
            </Col>
            <Col span={20} className="colClass">
              <Form.Item
                name={['check_table', 'members']}
                style={{ width: '100%', marginBottom: '0px' }}
                rules={[{ required: true, message: '请填写旁听成员' }]}
              >
                <Select
                  mode="multiple"
                  allowClear
                  placeholder="请填写旁听成员"
                  showSearch
                  filterOption={(input: string, option: any) =>
                    option.label.includes(input)
                  }
                  options={users.map((org: any) => ({
                    value: org.id,
                    label: org.full_name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass">
              <div>旁听会议</div>
            </Col>
            <Col span={20} className="colClass">
              <Form.Item
                name={['check_table', 'meeting_name']}
                style={{ width: '100%', marginBottom: '0px' }}
                rules={[{ required: true, message: '请填写旁听会议' }]}
              >
                <Input allowClear placeholder="请填写旁听会议" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={24} className="colClass">
              <div>检查项</div>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass">
              <div>主题</div>
            </Col>
            <Col span={16} className="colClass">
              <div>事项</div>
            </Col>
            <Col span={4} className="colClass">
              <div>开展情况</div>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass1">
              <div>现场旁听 (必填)</div>
            </Col>
            <Col span={20} style={{ padding: '0px' }}>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否以政治学习为主
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={['check_table', 'check_items', '是否以政治学习为主']}
                    rules={[{ required: true, message: '此项为必填项' }]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否坚持读原著、学原文、悟原理
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否坚持读原著、学原文、悟原理',
                    ]}
                    rules={[{ required: true, message: '此项为必填项' }]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否在会前进行个人自学
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否在会前进行个人自学',
                    ]}
                    rules={[{ required: true, message: '此项为必填项' }]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否结合实际充分开展集体研讨
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否结合实际充分开展集体讨论',
                    ]}
                    rules={[{ required: true, message: '此项为必填项' }]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否形成转化学习成果有效举措
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否形成转化学习成果有效举措',
                    ]}
                    rules={[{ required: true, message: '此项为必填项' }]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>
          <Row gutter={20} style={{ padding: '0px 20px' }}>
            <Col span={4} className="colClass1">
              <div>现场检查 (非必填)</div>
            </Col>
            <Col span={20} style={{ padding: '0px' }}>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否制定年度理论学习中心组学习计划
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否指定年度理论学习中心组学习计划',
                    ]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  是否建立理论学习中心组学习台账
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '是否建立理论学习中心组学习台账',
                    ]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  中心组成员是否列出年度自学清单
                </Col>
                <Col
                  style={{ width: '20%', padding: '30px 10px' }}
                  className="colClass2"
                >
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '中心组成员是否列出年度自学清单',
                    ]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  制修订中心组学习制度情况
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <div>
                    <div>
                      制定时间
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '制修订中心组学习制度情况',
                          '指定时间',
                        ]}
                        noStyle
                      >
                        <DatePicker />
                      </Form.Item>
                    </div>
                    <div>
                      修订时间
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '制修订中心组学习制度情况',
                          '修订时间',
                        ]}
                        noStyle
                      >
                        <DatePicker />
                      </Form.Item>
                    </div>
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  全年中心组学习次数情况
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <div
                    style={{
                      display: 'flex',
                      flexWrap: 'wrap',
                      textWrap: 'wrap',
                    }}
                  >
                    计划内
                    <div style={{ width: '64px', display: 'inline' }}>
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年中心组学习次数情况',
                          '计划内',
                        ]}
                        noStyle
                      >
                        <Input placeholder="" />
                      </Form.Item>
                    </div>
                    次, 计划外
                    <div style={{ width: '64px', display: 'inline' }}>
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年中心组学习次数情况',
                          '计划外',
                        ]}
                        noStyle
                      >
                        <Input placeholder="" />
                      </Form.Item>
                    </div>
                    次
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  全年开展集体研讨情况（每次至少三位发言）
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <div className="flex">
                    开展
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年开展集体研讨情况（每次至少三位发言）',
                          '开展',
                        ]}
                        noStyle
                      >
                        <Input />
                      </Form.Item>
                    </div>
                    次
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  两会、三中全会专题会是否全体进行研讨
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '两会、三中全会专题会是否全体进行研讨',
                    ]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  全年是否开展专题调查研究
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <Form.Item
                    name={[
                      'check_table',
                      'check_items',
                      '全年是否开展专题调查研究',
                    ]}
                  >
                    <Select
                      options={[
                        { value: true, label: '是' },
                        { value: false, label: '否' },
                      ]}
                      allowClear
                      placeholder="请选择"
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  全年形成学习报告情况
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <div className="flex">
                    形成简报
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年形成学习报告情况',
                          '形成简报',
                        ]}
                        noStyle
                      >
                        <Input />
                      </Form.Item>
                    </div>
                    篇
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{
                    width: '80%',
                    padding: '10px 10px',
                    height: '150px',
                  }}
                  className="colClass2"
                >
                  全年中心组成员出勤情况
                </Col>
                <Col
                  style={{ width: '20%', height: '150px' }}
                  className="colClass2 overflow-y-auto"
                >
                  <div className="wrap-break-word inline-flex flex-wrap break-normal">
                    全年累计请假
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年中心组成员出勤情况',
                          '请假',
                        ]}
                        noStyle
                      >
                        <Input className="inline-block" />
                      </Form.Item>
                    </div>
                    次, 完成补学
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年中心组成员出勤情况',
                          '补学',
                        ]}
                        noStyle
                      >
                        <Input className="inline-block" />
                      </Form.Item>
                    </div>
                    次, 其中单个中心组成员累计请假超过 2 次人员
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '全年中心组成员出勤情况',
                          '超过2次人员',
                        ]}
                        noStyle
                      >
                        <Input className="inline-block" />
                      </Form.Item>
                    </div>
                    人
                  </div>
                </Col>
              </Row>
              <Row>
                <Col
                  style={{ width: '80%', padding: '10px 10px' }}
                  className="colClass2"
                >
                  党委或班子成员在党报党刊刊发理论文章情况
                </Col>
                <Col style={{ width: '20%' }} className="colClass2">
                  <div className="flex">
                    2024年共
                    <div className="inline w-16">
                      <Form.Item
                        name={[
                          'check_table',
                          'check_items',
                          '党委或班子成员在党报党刊刊发理论文章情况',
                          '2024年共',
                        ]}
                        noStyle
                      >
                        <Input placeholder="次数" />
                      </Form.Item>
                    </div>
                    次
                  </div>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>

        <Flex className="my-4" gap="small" wrap justify="end">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() => navigate(`/examine/sit-in-on`)}
          >
            取消
          </Button>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="danger-btn"
            onClick={meetingInfoForm.submit}
            loading={loading}
          >
            提交反馈表
          </Button>
        </Flex>
      </div>
      <style lang="lass">
        {`
          .colClass{
            border: 1px solid #e9e9e9;
            color:#666666;
            display: flex;
            justify-content: center;
            align-items: center;
            height:70px;
          }
            .colClass1{
            border: 1px solid #e9e9e9;
            color:#666666;
            display: flex;
            justify-content: center;
            align-items: center;
            minHeight:70px;
          }
            .colClass2{
            border: 1px solid #e9e9e9;
            color:#666666;

            height:110px;
            }
        `}
      </style>
    </div>
  )
}

export default AttendanceAuditChecklist
