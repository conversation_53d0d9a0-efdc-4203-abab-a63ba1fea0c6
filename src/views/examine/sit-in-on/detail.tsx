import { FilePdfOutlined } from '@ant-design/icons'
import {
  Breadcrumb,
  Button,
  Card,
  Col,
  Descriptions,
  Flex,
  message,
  Row,
  Spin,
  Timeline,
} from 'antd'
import dayjs from 'dayjs'
import { useQueryState } from 'nuqs'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import { ApiResponse, service } from '~/lib/service'
import { fetchMeetingWatchCount } from '~/store/meeting'

interface BaseInfo {
  addr: string
  begin_time: string
  check_table: {
    check_items: {
      '两会、三中全会专题会是否全体进行研讨': boolean
      中心组成员是否列出年度自学清单: boolean
      党委或班子成员在党报党刊刊发理论文章情况: { '2024年共': number }
      全年中心组学习次数情况: { 计划内: number; 计划外: number }
      全年中心组成员出勤情况: {
        补学: number
        请假: number
        超过2次人员: number
      }
      '全年开展集体研讨情况（每次至少三位发言）': { 开展: number }
      全年形成学习报告情况: { 形成简报: number }
      全年是否开展专题调查研究: boolean
      制修订中心组学习制度情况: { 修订时间: string; 指定时间: string }
      是否以政治学习为主: boolean
      是否在会前进行个人自学: boolean
      '是否坚持读原著、学原文、悟原理': boolean
      是否建立理论学习中心组学习台账: boolean
      是否形成转化学习成果有效举措: boolean
      是否指定年度理论学习中心组学习计划: boolean
      是否结合实际充分开展集体讨论: boolean
    }
    meeting_name: string
    members: string[]
    members_name: string[]
    organization_id: string
    organization_name: string
    watch_time: string
  }
  created_at: string
  creator_company_id: string
  creator_id: string
  creator_name: string
  deleted_at: null
  end_time: string
  id: string
  meeting_name: string
  organization_id: string
  organization_name: string
  requirement: string
  resources: []
  sort_int: number
  status: string
  under_taker_id: string
  under_taker_name: string
  updated_at: string
}

interface TimelineItem {
  approved_at: null | string
  approver: string
  approver_id: string
  created_at: string
  deleted_at: null | string
  id: string
  is_active: boolean
  meeting_id: string
  node: string
  operator: string
  operator_id: string
  reason: string
  requester: string
  requester_id: string
  result: string
  sort_int: number
  type: string
  updated_at: string
}

const TIMELINE_TITLE = {
  create: '发起列席旁听',
  fill_data: '填写会议信息',
  fill_check: '反馈列席旁听检查表',
}

const ViewDetails: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [loading, setLoading] = useState(false)
  const [meetingDetail, setMeetingDetail] = useState<BaseInfo>()
  const [meetingTimeline, setMeetingTimeline] = useState<TimelineItem[]>()
  const [oaKey] = useQueryState('key')

  useMount(async () => {
    if (!oaKey) return
    await service.post<ApiResponse<unknown>>('/oaMsg/finish', {
      keys: [oaKey],
    })
    fetchMeetingWatchCount()
  })

  const initializeData = async () => {
    setLoading(true)
    try {
      const [detailRes, timelineRes] = await Promise.all([
        service.get<ApiResponse<{ base_info: BaseInfo }>>(
          '/evaluation/meetingWatch/detail',
          { params: { id } }
        ),
        service.get<ApiResponse<TimelineItem[]>>(`/workflows/${id}`, {
          params: { type: 'meeting_watch' },
        }),
      ])

      if (detailRes.code === 200001 && timelineRes.code === 200001) {
        setMeetingDetail(detailRes.data.base_info)
        setMeetingTimeline(timelineRes.data)
      } else {
        message.error(detailRes.message || timelineRes.message)
      }
    } catch (error) {
      message.error('获取数据失败')
      console.error('初始化数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const onFileDownload = async (fileId: string, name: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    const fileName = decodeURIComponent((name || '').split('/').pop() || 'file')
    try {
      const res = await service.get<Blob>(`/files/${fileId}/download`, {
        responseType: 'blob',
      })
      const blobUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      message.destroy()
      message.success('文件下载成功')
    } catch (error: any) {
      message.destroy()
      message.open({
        type: 'error',
        content: error?.message,
        duration: 2,
      })
    }
  }

  const boolText = (val: boolean | undefined) =>
    val === true ? '是' : val === false ? '否' : ''

  useMount(initializeData)

  return (
    <Spin spinning={loading}>
      <div className="flex h-full flex-col">
        <Breadcrumb
          separator=">"
          className="mb-4"
          items={[
            {
              title: '考核评价',
            },
            {
              title: '列席旁听',
              href: '#',
              onClick: () => navigate(`/examine/sit-in-on`),
            },
            { title: '会议详情' },
          ]}
        />

        <Row gutter={24} className="flex-1 overflow-auto">
          {/* 左侧内容区域 - 修改为 12 列 */}
          <Col span={12}>
            <Card bordered={false}>
              {/* 会议标题部分 */}
              <div className="mb-6">
                <div className="text-base font-bold">
                  {meetingDetail?.meeting_name}
                </div>
                <div className="mt-2 text-sm text-gray-400">
                  <div>创建时间：{meetingDetail?.created_at}</div>
                </div>
                <hr className="mt-4 border-t border-gray-200" />
              </div>

              {/* 会议信息部分 */}
              <div className="mb-6">
                <div className="mb-4 text-base font-bold">会议信息</div>
                <Descriptions
                  column={1}
                  labelStyle={{ width: '120px', textAlign: 'right' }}
                >
                  <Descriptions.Item label="会议开始时间">
                    {meetingDetail?.begin_time}
                  </Descriptions.Item>
                  <Descriptions.Item label="会议结束时间">
                    {meetingDetail?.end_time}
                  </Descriptions.Item>
                  <Descriptions.Item
                    label="会议材料"
                    contentStyle={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '4px',
                    }}
                  >
                    {meetingDetail?.resources?.map((item: any) => {
                      return (
                        <Flex
                          key={item.id}
                          className="w-full flex-1 border border-gray-200 p-2"
                          align="center"
                          justify="space-between"
                        >
                          {item.name !== '其他材料' && (
                            <div className="flex w-0 flex-1">
                              <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                              <span className="flex-1 truncate text-sm">
                                {item.name}
                              </span>
                            </div>
                          )}
                          <div className="flex-shrink-0 flex-grow-0">
                            {item.file_id && (
                              <Button
                                type="link"
                                size="large"
                                className="danger-btn-link text-sm"
                                onClick={() =>
                                  onFilePreview(item.file_id as string)
                                }
                              >
                                查看
                              </Button>
                            )}
                            <Button
                              type="link"
                              size="large"
                              className="danger-btn-link text-sm"
                              onClick={() => {
                                onFileDownload(item.file_id, item.name)
                              }}
                            >
                              下载
                            </Button>
                          </div>
                        </Flex>
                      )
                    })}
                  </Descriptions.Item>
                  <Descriptions.Item label="会议地点">
                    {meetingDetail?.addr}
                  </Descriptions.Item>
                  <Descriptions.Item label="被旁听单位">
                    {meetingDetail?.organization_name}
                  </Descriptions.Item>
                </Descriptions>
                <hr className="mt-4 border-t border-gray-200" />
              </div>
              {/* 列席旁听检查表 */}
              <div className="mb-6">
                <div className="mb-4 text-base font-bold">列席旁听检查表</div>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass">
                    <div>旁听时间</div>
                  </Col>
                  <Col span={20} className="colClass">
                    <div>{meetingDetail?.check_table.watch_time}</div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass">
                    <div>被旁听单位</div>
                  </Col>
                  <Col span={20} className="colClass">
                    <div>{meetingDetail?.check_table.organization_name}</div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass">
                    <div>旁听成员</div>
                  </Col>
                  <Col span={20} className="colClass">
                    <div>
                      {meetingDetail?.check_table.members_name?.join(';')}
                    </div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass">
                    <div>旁听地点</div>
                  </Col>
                  <Col span={20} className="colClass">
                    <div>{meetingDetail?.addr}</div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={24} className="colClass">
                    <div>检查项</div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass">
                    <div>主题</div>
                  </Col>
                  <Col span={16} className="colClass">
                    <div>事项</div>
                  </Col>
                  <Col span={4} className="colClass">
                    <div>开展情况</div>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass1">
                    <div>现场旁听</div>
                  </Col>
                  <Col span={20} style={{ padding: '0px' }}>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否以政治学习为主
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table?.check_items?.[
                            '是否以政治学习为主'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否坚持读原著、学原文、悟原理
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否坚持读原著、学原文、悟原理'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否在会前进行个人自学
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否在会前进行个人自学'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否结合实际充分开展集体研讨
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否结合实际充分开展集体讨论'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否形成转化学习成果有效举措
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否形成转化学习成果有效举措'
                          ]
                        )}
                      </Col>
                    </Row>
                  </Col>
                </Row>
                <Row gutter={20} style={{ padding: '0px 20px' }}>
                  <Col span={4} className="colClass1">
                    <div>现场检查</div>
                  </Col>
                  <Col span={20} style={{ padding: '0px' }}>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否制定年度理论学习中心组学习计划
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否指定年度理论学习中心组学习计划'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        是否建立理论学习中心组学习台账
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '是否建立理论学习中心组学习台账'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        中心组成员是否列出年度自学清单
                      </Col>
                      <Col
                        style={{ width: '20%', padding: '30px 10px' }}
                        className="colClass2"
                      >
                        {boolText(
                          meetingDetail?.check_table.check_items?.[
                            '中心组成员是否列出年度自学清单'
                          ]
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        制修订中心组学习制度情况
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        制定时间:
                        {dayjs(
                          meetingDetail?.check_table.check_items?.[
                            '制修订中心组学习制度情况'
                          ]?.['指定时间']
                        ).format('YYYY-MM-DD')}
                        <br />
                        修订时间:
                        {dayjs(
                          meetingDetail?.check_table.check_items?.[
                            '制修订中心组学习制度情况'
                          ]?.['修订时间']
                        ).format('YYYY-MM-DD')}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        全年中心组学习次数情况
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        计划内
                        {
                          meetingDetail?.check_table.check_items?.[
                            '全年中心组学习次数情况'
                          ]?.['计划内']
                        }
                        次, 计划外
                        {
                          meetingDetail?.check_table.check_items?.[
                            '全年中心组学习次数情况'
                          ]?.['计划外']
                        }
                        次
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        全年开展集体研讨情况（每次至少三位发言）
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        开展
                        {
                          meetingDetail?.check_table.check_items?.[
                            '全年开展集体研讨情况（每次至少三位发言）'
                          ]?.['开展']
                        }
                        次
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        两会、三中全会专题会是否全体进行研讨
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        {meetingDetail?.check_table.check_items?.[
                          '两会、三中全会专题会是否全体进行研讨'
                        ]
                          ? '是'
                          : '否'}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        全年是否开展专题调查研究
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        {meetingDetail?.check_table.check_items?.[
                          '全年是否开展专题调查研究'
                        ]
                          ? '是'
                          : '否'}
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        全年形成学习报告情况
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        形成简报
                        {
                          meetingDetail?.check_table.check_items?.[
                            '全年形成学习报告情况'
                          ]?.['形成简报']
                        }
                        篇
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        全年中心组成员出勤情况
                      </Col>
                      <Col
                        style={{ width: '20%' }}
                        className="colClass2 overflow-y-auto"
                      >
                        全年累计请假
                        {
                          meetingDetail?.check_table?.check_items?.[
                            '全年中心组成员出勤情况'
                          ]?.['请假']
                        }
                        次, 完成补学
                        {
                          meetingDetail?.check_table?.check_items?.[
                            '全年中心组成员出勤情况'
                          ]?.['补学']
                        }
                        次, 其中单个中心组成员累计请假超过 2 次人员
                        {
                          meetingDetail?.check_table?.check_items?.[
                            '全年中心组成员出勤情况'
                          ]?.['超过2次人员']
                        }
                        人
                      </Col>
                    </Row>
                    <Row>
                      <Col
                        style={{ width: '80%', padding: '10px 10px' }}
                        className="colClass2"
                      >
                        党委或班子成员在党报党刊刊发理论文章情况
                      </Col>
                      <Col style={{ width: '20%' }} className="colClass2">
                        2024年共
                        {
                          meetingDetail?.check_table?.check_items?.[
                            '党委或班子成员在党报党刊刊发理论文章情况'
                          ]?.['2024年共']
                        }
                        次
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </div>
            </Card>
          </Col>

          {/* 右侧内容区域 */}
          <Col span={12}>
            {/* 工作流程卡片 */}
            <Card bordered={false}>
              <div className="mb-4 text-base font-bold">工作流程</div>
              <Timeline
                items={meetingTimeline?.map((item) => ({
                  dot: (
                    <div
                      style={{
                        width: '15px',
                        height: '15px',
                        borderRadius: '50%',
                        backgroundColor: item.is_active
                          ? 'rgba(211, 47, 47, 1)'
                          : 'rgba(141, 110, 99, 1)',
                      }}
                    ></div>
                  ),
                  children: (
                    <div className="py-1">
                      <div className="text-sm">
                        {
                          TIMELINE_TITLE[
                            item.result as keyof typeof TIMELINE_TITLE
                          ]
                        }
                      </div>
                      {item.operator && (
                        <div className="text-sm text-gray-400">
                          操作人：{item.operator}
                        </div>
                      )}
                      <div className="text-sm text-gray-400">
                        操作时间：{item.updated_at}
                      </div>
                    </div>
                  ),
                }))}
              />
            </Card>
          </Col>
        </Row>
        <style lang="lass">
          {`
          .colClass{
            border: 1px solid #e9e9e9;
            color:#666666;
            display: flex;
            justify-content: center;
            align-items: center;
            height:70px;
          }
            .colClass1{
            border: 1px solid #e9e9e9;
            color:#666666;
            display: flex;
            justify-content: center;
            align-items: center;
            minHeight:70px;
          }
            .colClass2{
            border: 1px solid #e9e9e9;
            color:#666666;
            min-height:110px;
            }
        `}
        </style>
      </div>
    </Spin>
  )
}

export default ViewDetails
