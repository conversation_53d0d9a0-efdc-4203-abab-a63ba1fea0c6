import { ExclamationCircleFilled } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON>rumb,
  Button,
  Flex,
  Input,
  Modal,
  Select,
  Tabs,
  message,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useMount } from 'react-use'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { PageParams } from '~/models/common/pageParams'
import { useAuthStore } from '~/store/auth'
import { flattenedData } from '~/utils'

interface TableItem {
  list: {
    id: string
    organization_id: string
    organization_name: string
    organization_short_name: string
    meeting_name: string
    begin_time: string
    end_time: string
    under_taker_id: string
    under_taker_name: string
    status: string
    requirement: string
    creator_company_id: string
    creator_id: string
    creator_name: string
    created_at: string
    updated_at: string
    deleted_at: string | null
    operations: {
      fill_data: boolean
      fill_check: boolean
      delete: boolean
    }
  }[]
  pager: {
    total: number
    current: number
    pageSize: number
  }
}

const SitInOn: React.FC = () => {
  const navigate = useNavigate()
  const { confirm } = Modal
  const [tableData, setTableData] = useState<TableItem['list']>([])
  const [tableType, setTableType] = useState('')

  const [modalVisible, setModalVisible] = useState(false)
  const [viewRequirementVisible, setViewRequirementVisible] = useState(false)

  const [requirement, setRequirement] = useState('')

  const [orgs, setOrgs] = useState<any>([])

  const [createRequirement, setCreateRequirement] = useState('')
  const [createOrganizationId, setCreateOrganizationId] = useState('')
  const [createLoading, setCreateLoading] = useState(false)

  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })
  const [deleting, setDeleting] = useState(false)

  const { userInfo } = useAuthStore()

  const handleCreateConfirm = async () => {
    if (!createRequirement.trim() || !createOrganizationId) {
      message.warning('请填写列席旁听工作要求和被旁听单位')
      return
    }

    setCreateLoading(true)
    try {
      const res = await service.post<ApiResponse<any>>(
        '/evaluation/meetingWatch',
        {
          requirement: createRequirement,
          organization_id: createOrganizationId,
        }
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return
      }

      message.success('发起成功')
      setModalVisible(false)
      setCreateRequirement('')
      setCreateOrganizationId('')
      setPageParams({
        ...pageParams,
        current: 1,
      })
    } catch (error) {
      message.error('创建失败')
      console.log(error)
    } finally {
      setCreateLoading(false)
    }
  }

  const getTableData = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<TableItem>>(
        '/evaluation/meetingWatch',
        {
          params: { ...pageParams, tab: tableType },
        }
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return []
      }

      setTableData(res.data.list)
      setTotal(res.data.pager.total)
    } finally {
      setLoading(false)
    }
  }

  const fetchOrgs = async () => {
    try {
      const response = await service.get<ApiResponse<any>>('/orgs/son')
      if (response.code !== 200001) {
        message.error(response.message)
        return
      }
      setOrgs(flattenedData(response.data))
    } catch (error) {
      message.error('获取组织列表失败')
      console.log(error)
    }
  }

  useEffect(() => {
    getTableData()
  }, [pageParams, tableType])

  useMount(() => {
    fetchOrgs()
  })

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '列席旁听会议名称',
      dataIndex: 'meeting_name',
      key: 'meeting_name',
      width: 170,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '发起人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
      align: 'left',
    },
    {
      title: '承办人',
      dataIndex: 'under_takers',
      key: 'under_takers',
      width: 100,
      align: 'left',
      render: (val) => {
        if (!val || val.length === 0) return null
        return val.map((item: any) => item.under_taker_name).join(',')
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 200,
      align: 'left',
    },
    {
      title: '会议开始时间',
      dataIndex: 'begin_time',
      key: 'begin_time',
      width: 200,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '会议结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 200,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 260,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <div className="flex justify-center" style={{ flexWrap: 'wrap' }}>
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => {
              setViewRequirementVisible(true)
              setRequirement(record.requirement)
            }}
          >
            列席旁听要求
          </Button>
          {record.operations.fill_data && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() =>
                navigate(`/examine/sit-in-on/meetingInformation/${record.id}`)
              }
            >
              填写会议信息
            </Button>
          )}
          {record.operations.fill_check && !record.check_table_time && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() =>
                navigate(
                  `/examine/sit-in-on/attendanceAuditChecklist/${record.id}`
                )
              }
            >
              填写检查表
            </Button>
          )}

          {record.status === '已结束' && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() => {
                const underTakerIds = (record.under_takers || []).map(
                  (item: any) => item.under_taker_id
                )
                navigate(`/examine/sit-in-on/reformRecord/${record.id}`, {
                  state: {
                    role:
                      userInfo?.id === record.creator_id
                        ? 'creator'
                        : userInfo?.id === record.under_taker_id ||
                            underTakerIds.includes(userInfo?.id)
                          ? 'under_taker'
                          : 'user',
                  },
                })
              }}
            >
              整改台账
            </Button>
          )}

          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`/examine/sit-in-on/detail/${record.id}`)}
          >
            查看详情
          </Button>

          {record.operations.delete && (
            <Button
              type="link"
              size="large"
              className="danger-btn-link"
              onClick={() => handleRowDelete(record)}
            >
              删除
            </Button>
          )}
        </div>
      ),
    },
  ] as ColumnsType<DataType>

  const handleRowDelete = (row: any) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该列席旁听?',
      centered: true,
      okButtonProps: {
        loading: deleting,
      },
      async onOk() {
        setDeleting(true)
        try {
          const res = await service.delete<ApiResponse<any>>(
            `/evaluation/meetingWatch`,
            {
              data: {
                ids: [row.id],
              },
            }
          )
          if (res.code !== 200001) {
            return message.error(res.message)
          }
          message.success('删除成功')
          getTableData()
        } finally {
          setDeleting(false)
        }
      },
    })
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '考核评价' }, { title: '列席旁听' }]}
      />

      <Tabs
        defaultActiveKey="0"
        onChange={(value) => setTableType(value)}
        items={[
          { label: '所有会议', key: '0' },
          { label: '我发起的', key: '1' },
          { label: '我承办的', key: '2' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="primary-btn"
              onClick={() => setModalVisible(true)}
            >
              发起列席旁听
            </Button>
          </div>
        </Flex>
        <CustomTable
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={total}
          loading={loading}
          onChange={(page, pageSize) => {
            setPageParams({ current: page, pageSize })
          }}
        ></CustomTable>
      </div>

      <Modal
        title="发起列席旁听"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button
            key="submit"
            type="primary"
            danger
            onClick={handleCreateConfirm}
            loading={createLoading}
          >
            确认发起
          </Button>,
        ]}
        centered
      >
        <div>
          <Select
            allowClear
            placeholder="请选择被旁听单位"
            showSearch
            filterOption={(input: string, option: any) =>
              option.label.includes(input)
            }
            options={orgs.map((org: any) => ({
              value: org.id,
              label: org.name,
            }))}
            style={{ width: '100%' }}
            value={createOrganizationId}
            onChange={(value) => setCreateOrganizationId(value)}
          />
        </div>
        <div className="py-4">
          <Input.TextArea
            rows={4}
            placeholder="请输入列席旁听工作要求"
            value={createRequirement}
            onChange={(e) => setCreateRequirement(e.target.value)}
          />
        </div>
      </Modal>

      <Modal
        title="列席旁听要求"
        open={viewRequirementVisible}
        onCancel={() => {
          setViewRequirementVisible(false)
          setRequirement('')
        }}
        footer={null}
        centered
      >
        <div className="mt-4 border-t border-gray-200 pt-4">
          <div className="text-gray-700">{requirement}</div>
        </div>
      </Modal>
    </div>
  )
}

export default SitInOn
