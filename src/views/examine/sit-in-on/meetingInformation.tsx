import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>icker,
  Flex,
  Form,
  Input,
  Row,
  Select,
  message,
} from 'antd'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import FileUpload from '~/components/upload-file'
import { ApiResponse, service } from '~/lib/service'
import { fetchMeetingWatchCount } from '~/store/meeting'
import { flattenedData } from '~/utils'

const MeetingInformation: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const [meetingInfoForm] = Form.useForm()

  const [orgs, setOrgs] = useState<any>([])
  const [loading, setLoading] = useState(false)

  const onCreatePlanFinish = async (values: any) => {
    setLoading(true)
    const submitData = {
      ...values,
      begin_time: dayjs(values.begin_time).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(values.end_time).format('YYYY-MM-DD HH:mm:ss'),
    }

    try {
      const response = await service.put<ApiResponse<null>>(
        '/evaluation/meetingWatch',
        {
          id,
          ...submitData,
        }
      )

      if (response.code !== 200001) {
        message.error(response.message)
      }

      navigate('/examine/sit-in-on')
      fetchMeetingWatchCount()
    } catch (error) {
      message.error('提交失败')
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  const fetchOrgs = async () => {
    try {
      const response = await service.get<ApiResponse<any>>(
        '/orgs/tree?self=true'
      )
      if (response.code !== 200001) {
        message.error(response.message)
        return
      }
      setOrgs(flattenedData(response.data))
    } catch (error) {
      message.error('获取组织列表失败')
      console.log(error)
    }
  }

  const getMeetingDetail = async () => {
    setLoading(true)
    try {
      const res = await service.get<ApiResponse<any>>(
        '/evaluation/meetingWatch/detail',
        {
          params: { id },
        }
      )
      if (res.code !== 200001) {
        message.error(res.message)
        return
      }
      meetingInfoForm.setFieldsValue({
        meeting_name: res.data.base_info.meeting_name,
        begin_time: res.data.base_info.begin_time
          ? dayjs(res.data.base_info.begin_time)
          : '',
        end_time: res.data.base_info.end_time
          ? dayjs(res.data.base_info.end_time)
          : '',
        addr: res.data.base_info.addr,
        organization_id: res.data.base_info.organization_id,
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrgs()
    getMeetingDetail()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价' },
          { title: '列席旁听', href: '/examine/sit-in-on' },
          { title: '填写会议信息' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <p className="mb-4 font-semibold">会议信息</p>
        <Form
          form={meetingInfoForm}
          name="meetingInfoForm"
          initialValues={{ selfLearningTheme: '' }}
          clearOnDestroy
          onFinish={onCreatePlanFinish}
          autoComplete="off"
          labelCol={{ span: 2 }}
          labelAlign="left"
          className="hide-scrollbar w-full flex-1 overflow-auto overflow-x-hidden"
          disabled={loading}
        >
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="会议名称"
                name="meeting_name"
                rules={[
                  { required: true, message: '请填写会议名称' },
                  { max: 30, message: '最多30个字符' },
                ]}
              >
                <Input allowClear placeholder="请填写会议名称" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="会议开始时间"
                labelCol={{ span: 4 }}
                name="begin_time"
                rules={[{ required: true, message: '请选择会议开始时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="会议结束时间"
                labelCol={{ span: 4 }}
                name="end_time"
                rules={[{ required: true, message: '请选择会议结束时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  width={'100%'}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="会议材料" name="resources">
                <FileUpload maxCount={Infinity} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="会议地点" name="addr">
                <Input allowClear placeholder="请填写会议地点" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="被旁听单位"
                name="organization_id"
                rules={[{ required: true, message: '请填写被旁听单位' }]}
              >
                <Select
                  allowClear
                  placeholder="请选择被旁听单位"
                  showSearch
                  filterOption={(input: string, option: any) =>
                    option.label.includes(input)
                  }
                  options={orgs.map((org: any) => ({
                    value: org.id,
                    label: org.name,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Flex className="my-4" gap="small" wrap justify="end">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() => navigate(`/examine/sit-in-on`)}
          >
            取消
          </Button>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="danger-btn"
            onClick={meetingInfoForm.submit}
            loading={loading}
          >
            提交会议信息
          </Button>
        </Flex>
      </div>
    </div>
  )
}

export default MeetingInformation
