import { PlusOutlined } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Button,
  Flex,
  Form,
  Input,
  message,
  Select,
  Spin,
} from 'antd'
import { ColumnsType } from 'antd/lib/table'
import { useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useMount } from 'react-use'
import { v4 as uuidv4 } from 'uuid'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'

type RecordListItem = {
  id: string
  sort_int?: number
  meeting_watch_id?: string
  title: string
  rectification_type: string
  rectification_deadline: string
  rectification_effectiveness: string
  status: string
  created_at?: string
  updated_at?: string
  deleted_at?: null | string
}

export const ReformRecord: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { id } = useParams()
  const role = location.state?.role

  const [form] = Form.useForm()

  const [tableData, setTableData] = useState<RecordListItem[]>([])
  const [loading, setLoading] = useState(false)

  const [isLaunchAble, setIsLaunchAble] = useState(false)
  const [editingRowId, setEditingRowId] = useState<null | string>(null)

  const fetchRecord = async () => {
    setLoading(true)
    try {
      const res = await service.post<ApiResponse<RecordListItem[]>>(
        '/evaluation/meetingWatch/ledger/list',
        { meeting_watch_id: id }
      )

      if (res.code !== 200001) {
        return message.error(res.message)
      }

      setTableData(res.data || [])

      if (res.data?.length === 0 && role === 'creator') {
        setIsLaunchAble(true)
      } else {
        setIsLaunchAble(false)
      }

      const formObj: Record<string, any> = {}

      res.data.forEach((item) => {
        formObj[item.id] = {
          title: item.title,
          rectification_type: item.rectification_type,
          rectification_deadline: item.rectification_deadline,
          rectification_effectiveness: item.rectification_effectiveness,
          status: item.status,
        }
      })

      form.setFieldsValue(formObj)
    } catch (error) {
      message.error('获取数据失败')
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  const handleRecordDelete = async (id: string) => {
    setLoading(true)

    try {
      const res = await service.post<ApiResponse<null>>(
        '/evaluation/meetingWatch/ledger/delete',
        { id }
      )

      if (res.code !== 200001) {
        return message.error(res.message)
      }

      message.success('删除成功')
      fetchRecord()
    } catch (error) {
      message.error('删除失败')
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  const handleRecordLaunch = async (values: Record<string, RecordListItem>) => {
    setLoading(true)

    const paramsKeys = Object.keys(values)
    const params = paramsKeys.map((key) => {
      return { ...values[key], id: key }
    })

    try {
      const res = await service.post<ApiResponse<null>>(
        '/evaluation/meetingWatch/ledger/initiate',
        {
          meeting_watch_id: id,
          items: params,
        }
      )

      if (res.code !== 200001) {
        return message.error(res.message)
      }
      message.success('发起成功')
      navigate('/examine/sit-in-on')
    } catch (error) {
      message.error('发起失败')
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  const handleRecordUpdate = async (
    id: string,
    params: Record<string, string>
  ) => {
    setLoading(true)

    const currentData = form.getFieldValue(id)

    try {
      const res = await service.post<ApiResponse<null>>(
        '/evaluation/meetingWatch/ledger/update',
        { id, ...currentData, ...params }
      )
      if (res.code !== 200001) {
        return message.error(res.message)
      }

      message.success('更新成功')
      fetchRecord()
    } catch (error) {
      message.error('更新失败')
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  useMount(() => {
    fetchRecord()
  })

  const columns = [
    {
      title: '意见反馈',
      dataIndex: 'title',
      key: 'title',
      render: (_, record) =>
        (isLaunchAble || editingRowId === record.id) && role === 'creator' ? (
          <Form.Item name={[record.id, 'title']}>
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 3 }}
              placeholder="请输入意见反馈"
            />
          </Form.Item>
        ) : (
          <span>{record.title}</span>
        ),
    },
    {
      title: '整改类型',
      dataIndex: 'rectification_type',
      key: 'rectification_type',
      render: (_, record) =>
        (isLaunchAble || editingRowId === record.id) && role === 'creator' ? (
          <Form.Item name={[record.id, 'rectification_type']}>
            <Select
              placeholder="请选择整改类型"
              options={[
                { label: '立行立改', value: '立行立改' },
                { label: '长期整改', value: '长期整改' },
              ]}
            />
          </Form.Item>
        ) : (
          <span>{record.rectification_type}</span>
        ),
    },
    {
      title: '整改时限',
      dataIndex: 'rectification_deadline',
      key: 'rectification_deadline',
      render: (_, record) =>
        (isLaunchAble || editingRowId === record.id) && role === 'creator' ? (
          <Form.Item name={[record.id, 'rectification_deadline']}>
            <Input placeholder="请输入整改时限" />
          </Form.Item>
        ) : (
          <span>{record.rectification_deadline}</span>
        ),
    },
    {
      title: '整改成效',
      dataIndex: 'rectification_effectiveness',
      key: 'rectification_effectiveness',
      render: (_, record) =>
        isLaunchAble || editingRowId === record.id ? (
          <Form.Item name={[record.id, 'rectification_effectiveness']}>
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 3 }}
              placeholder="请输入整改成效"
            />
          </Form.Item>
        ) : (
          <span>{record.rectification_effectiveness}</span>
        ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) =>
        (isLaunchAble || editingRowId === record.id) && role === 'creator' ? (
          <Form.Item name={[record.id, 'status']} initialValue="进行中">
            <Select
              placeholder="请选择状态"
              options={[
                { label: '进行中', value: '进行中' },
                { label: '已完成', value: '已完成' },
                { label: '未完成', value: '未完成' },
                { label: '已退回', value: '已退回' },
              ]}
            />
          </Form.Item>
        ) : (
          <span>{record.status}</span>
        ),
    },
    {
      title: '操作',
      render: (_, record: RecordListItem) => {
        if (role === 'user') {
          return null
        }
        if (isLaunchAble) {
          return (
            <>
              <Button
                danger
                onClick={() =>
                  setTableData((prev) =>
                    prev.filter((item) => item.id !== record.id)
                  )
                }
                type="link"
                disabled={false}
              >
                删除
              </Button>
            </>
          )
        } else {
          return editingRowId === record.id ? (
            <>
              <Button
                onClick={() => {
                  setEditingRowId(null)
                  handleRecordUpdate(record.id, { status: '进行中' })
                }}
                type="link"
              >
                保存
              </Button>
              <Button onClick={() => setEditingRowId(null)} type="link" danger>
                取消
              </Button>
            </>
          ) : (
            <>
              {record.status === '进行中' && role === 'creator' && (
                <>
                  <Button
                    disabled={false}
                    onClick={() =>
                      handleRecordUpdate(record.id, { status: '已完成' })
                    }
                    type="link"
                  >
                    核验无误
                  </Button>
                  <Button
                    disabled={false}
                    onClick={() =>
                      handleRecordUpdate(record.id, { status: '已退回' })
                    }
                    type="link"
                  >
                    退回
                  </Button>
                  <Button
                    disabled={false}
                    onClick={() =>
                      handleRecordUpdate(record.id, { status: '未完成' })
                    }
                    type="link"
                  >
                    设为未完成
                  </Button>
                </>
              )}

              {record.status === '未完成' && role === 'creator' && (
                <Button
                  onClick={() =>
                    handleRecordUpdate(record.id, { status: '进行中' })
                  }
                  type="link"
                  disabled={false}
                >
                  重新发布
                </Button>
              )}

              {(record.status === '进行中' || record.status === '已退回') && (
                <Button
                  disabled={false}
                  onClick={() => setEditingRowId(record.id)}
                  type="link"
                >
                  编辑
                </Button>
              )}

              {role === 'creator' && (
                <Button
                  danger
                  onClick={() => handleRecordDelete(record.id)}
                  type="link"
                  disabled={false}
                >
                  删除
                </Button>
              )}
            </>
          )
        }
      },
    },
  ] as ColumnsType<DataType>

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '考核评价', href: '/examine/annual-report' },
          { title: '列席旁听', href: '/examine/sit-in-on' },
          { title: '整改台账' },
        ]}
      />

      <Spin spinning={loading}>
        <div className="mt-4 flex h-0 flex-1 flex-col bg-white">
          <div className="flex items-center justify-between">
            <p className="w-full p-4 font-semibold">整改工作台账</p>
            <Button
              type="link"
              target="_blank"
              href={`/examine/sit-in-on/detail/${id}`}
            >
              查看会议详情
            </Button>
          </div>
          <Form
            form={form}
            onFinish={handleRecordLaunch}
            labelCol={{ span: 2 }}
            labelAlign="left"
            className="flex h-0 flex-1 flex-col p-4"
            // disabled={!isLaunchAble}
          >
            <CustomTable
              key="id"
              columns={columns}
              dataSource={tableData}
              pagination={false}
            ></CustomTable>

            {isLaunchAble && (
              <Button
                onClick={() => {
                  const id = uuidv4()
                  setTableData((prev) => [
                    ...prev,
                    {
                      id,
                      title: '',
                      rectification_type: '',
                      rectification_deadline: '',
                      rectification_effectiveness: '',
                      status: '',
                    },
                  ])
                  setEditingRowId(id)
                }}
              >
                <PlusOutlined /> 添加一条台账
              </Button>
            )}
          </Form>
          <Flex justify="end" gap="small" className="w-full p-4">
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="secondary-btn-outlined"
              onClick={() => navigate(-1)}
            >
              取消
            </Button>

            {isLaunchAble && (
              <Button
                color="primary"
                variant="solid"
                size="large"
                className="danger-btn"
                onClick={() => form.submit()}
              >
                发起
              </Button>
            )}
          </Flex>
        </div>
      </Spin>
    </div>
  )
}
