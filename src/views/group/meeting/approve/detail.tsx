import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Card,
  Descriptions,
  Timeline,
  Button,
  Row,
  Col,
  Radio,
  Input,
  Spin,
  Flex,
  Modal,
} from 'antd'
import { useState } from 'react'
import { message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import { useAsync } from 'react-use'
import dayjs from 'dayjs'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import {
  approveLeaveFlow,
  queryLeave,
  queryLeaveFlowAction,
  queryLeaveFlows,
} from '~/apis/group/meeting/approve/index'
import { Leave } from '~/models/group/meeting/approve'
import { Workflow } from '~/models/common/workflow'
import Permission from '~/components/permission'
import { getMeetingName } from '~/lib/utils'

const MeetingLeaveDetail: React.FC = () => {
  const { confirm } = Modal
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()

  const { loading } = useAsync(async () => {
    getFlowAction()
    await getDetail()
    await getFlows()
  }, [id])

  const [detail, setDetail] = useState<Leave>()
  const getDetail = async () => {
    const query = new URLSearchParams(window.location.search)
    const oaKey = query.get('key')
    const res = await queryLeave(id!, {
      key: oaKey,
    })
    if (res.code !== 200001) {
      message.error(`审批详情获取失败：${res.message}`)
    } else {
      setDetail(res.data)
    }
  }

  const [workFlows, setWorkFlows] = useState<Workflow[]>([])
  const getFlows = async () => {
    const res = await queryLeaveFlows(id!)
    if (res.code !== 200001) {
      message.error(`工作流程获取失败：${res.message}`)
    } else {
      setWorkFlows(res.data)
    }
  }

  const [flowAction, setFlowAction] = useState(false)
  const getFlowAction = () => {
    queryLeaveFlowAction(id!).then((res) => {
      if (res.code === 200001) {
        setFlowAction(res.data)
      }
    })
  }

  const [approveResult, setLeaveResult] = useState<'pass' | 'reject'>('pass')
  const [approveReason, setLeaveReason] = useState('')
  const handleMeetingLeave = () => {
    confirm({
      title: `确定要审批${approveResult === 'pass' ? '通过' : '驳回'}该请假申请吗？`,
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        if (approveResult !== 'pass' && !approveReason.trim()) {
          return message.warning('请输入驳回原因')
        }
        approveLeaveFlow(id!, approveResult, approveReason).then((res) => {
          if (res.code !== 200001) {
            message.error(`审批失败：${res.message}`)
          } else {
            message.success('审批成功')
            navigate(`/group/meeting/approve`)
          }
        })
      },
    })
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        className="mb-4"
        items={[
          {
            title: '集中学习',
            href: '#',
            onClick: () => navigate(`/group/meeting`),
          },
          {
            title: '会议请假审批',
            href: '#',
            onClick: () => navigate(`/group/meeting/approve`),
          },
          { title: '审批详情' },
        ]}
      />

      <Spin
        spinning={loading}
        wrapperClassName="w-full h-0 flex-1 hide-scrollbar overflow-x-hidden"
      >
        <Row gutter={24} className="flex-1 overflow-auto">
          <Col span={12}>
            <Card bordered={false}>
              <div className="mb-6">
                <div className="mb-4 text-base font-bold">请假信息</div>
                <Descriptions
                  column={1}
                  labelStyle={{ width: '120px', textAlign: 'right' }}
                >
                  <Descriptions.Item label="申请人">
                    {detail?.requester}
                  </Descriptions.Item>
                  <Descriptions.Item label="申请时间">
                    {detail?.request_at &&
                      dayjs(detail.request_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="请假类型">
                    {detail?.leave_type}
                  </Descriptions.Item>
                  <Descriptions.Item label="请假原因">
                    {detail?.request_reason}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批状态">
                    {detail?.status === 'approved' && (
                      <p className="text-[#4caf50]">已同意</p>
                    )}
                    {detail?.status === 'rejected' && '审批驳回'}
                    {detail?.status === 'pending' && '审批中'}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批人">
                    {detail?.approver}
                  </Descriptions.Item>
                  <Descriptions.Item label="审批时间">
                    {detail?.approved_at &&
                      dayjs(detail.approved_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                </Descriptions>
                <hr className="mt-4 border-t border-gray-200" />
              </div>

              <div className="mb-6">
                <div className="mb-4 text-base font-bold">会议信息</div>
                <Descriptions
                  column={1}
                  labelStyle={{ width: '120px', textAlign: 'right' }}
                >
                  <Descriptions.Item label="会议名称">
                    {getMeetingName(detail as any, {
                      nameKey: 'meeting_name',
                      yearKey: 'meeting_year',
                      countKey: 'meeting_count',
                    })}
                  </Descriptions.Item>
                  <Descriptions.Item label="会议开始时间">
                    {detail?.meeting_start_at &&
                      dayjs(detail.meeting_start_at).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                  </Descriptions.Item>
                  <Descriptions.Item label="会议结束时间">
                    {detail?.meeting_end_at &&
                      dayjs(detail.meeting_end_at).format(
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                  </Descriptions.Item>
                </Descriptions>
              </div>
            </Card>
          </Col>

          <Col span={12}>
            {flowAction && (
              <Permission roles={['first_resp']} codes={[]}>
                <Card bordered={false} className="mb-4">
                  <div className="mb-4 text-base font-bold">审批批示</div>
                  <Radio.Group
                    value={approveResult}
                    className="flex gap-8"
                    onChange={(e) => setLeaveResult(e.target.value)}
                  >
                    <Radio
                      value="pass"
                      style={{ color: '#4caf50', fontWeight: 'bold' }}
                    >
                      通过
                    </Radio>
                    <Radio
                      value="reject"
                      style={{
                        color: '#d32f2f',
                        fontWeight: 'bold',
                      }}
                    >
                      驳回
                    </Radio>
                  </Radio.Group>
                  <div className="mt-2">
                    <p className="my-2 text-[#333333]">
                      {approveResult === 'pass' ? '批示意见' : '驳回原因'}
                    </p>
                    <Input.TextArea
                      value={approveReason}
                      placeholder={`请填写您的${approveResult === 'pass' ? '批示意见' : '驳回原因'}`}
                      onChange={(e) => setLeaveReason(e.target.value)}
                      rows={3}
                    />
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button
                      color="primary"
                      variant="solid"
                      className="danger-btn"
                      onClick={handleMeetingLeave}
                    >
                      确认
                    </Button>
                  </div>
                </Card>
              </Permission>
            )}
            <Card bordered={false}>
              <div className="mb-4 text-base font-bold">工作流程</div>
              <Timeline
                items={workFlows.map((item) => ({
                  children: (
                    <Flex
                      vertical
                      gap="small"
                      className="text-sm text-[#999999]"
                    >
                      {item.node === 'end' && (
                        <>
                          <p className="text-base font-semibold text-[#333333]">
                            全部审核通过
                          </p>
                          <p>提交时间：{item.created_at}</p>
                        </>
                      )}
                      {(item.node === 'officer' ||
                        item.node === 'leader' ||
                        item.node === 'dept_leader' ||
                        item.node === 'dept_auditor') && (
                        <>
                          <p className="text-base font-semibold text-[#333333]">
                            {item.result === 'pass' && '审批通过'}
                            {item.result === 'reject' && '审批驳回'}
                            {item.result === 'pending' && '请假审批中'}
                            {item.result === 'cancel' && '取消'}
                          </p>
                          <p>审批人：{item.approver}</p>
                          {item.result !== 'reject' && (
                            <p>审批意见：{item.reason}</p>
                          )}
                          {item.result === 'reject' && (
                            <p className="text-[#d32f2f]">
                              驳回原因：{item.reason}
                            </p>
                          )}
                          <p>审批时间：{item.approved_at}</p>
                        </>
                      )}

                      {item.node === 'start' && (
                        <>
                          <p className="text-base font-semibold text-[#333333]">
                            请假申请
                          </p>
                          <p>操作人：{item.operator}</p>
                          <p>操作时间：{item.created_at}</p>
                        </>
                      )}
                    </Flex>
                  ),
                }))}
              />
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  )
}

export default MeetingLeaveDetail
