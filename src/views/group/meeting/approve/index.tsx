import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lex, Tabs } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import { queryLeaveList } from '~/apis/group/meeting/approve/index'
import { Leave } from '~/models/group/meeting/approve/index'
import { PageParams } from '~/models/common/pageParams'
import { useAuthStore } from '~/store/auth'
import { getMeetingName } from '~/lib/utils'

const MeetingLeave: React.FC = () => {
  const navigate = useNavigate()
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
    leave_request_type: 'request',
  })
  const [tableData, setTableData] = useState<Leave[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [tableLoading, setTableLoading] = useState(false)

  const { userInfo } = useAuthStore()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '申请人',
      dataIndex: 'requester',
      key: 'requester',
      width: 120,
      align: 'left',
    },
    {
      title: '申请时间',
      dataIndex: 'request_at',
      key: 'request_at',
      width: 180,
      align: 'left',
      render: (value) => value && dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '审批状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'left',
      render: (value) => {
        switch (value) {
          case 'approved':
            return '审批通过'
          case 'rejected':
            return '审批驳回'
          case 'pending':
            return '审批中'
        }
      },
    },
    {
      title: '审批人',
      dataIndex: 'approver',
      key: 'approver',
      width: 120,
      align: 'left',
    },
    {
      title: '审批时间',
      dataIndex: 'approved_at',
      key: 'approved_at',
      width: 180,
      align: 'left',
      render: (value) => value && dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '会议名称',
      dataIndex: 'meeting_name',
      key: 'meeting_name',
      width: 200,
      align: 'left',
      render: (_, record) =>
        getMeetingName(record, {
          nameKey: 'meeting_name',
          yearKey: 'meeting_year',
          countKey: 'meeting_count',
        }),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 260,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <div className="flex">
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`${record.id}`)}
          >
            查看审批详情
          </Button>
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`/group/meeting/${record.meeting_id}`)}
          >
            查看会议详情
          </Button>
        </div>
      ),
    },
  ] as ColumnsType<DataType>

  const tabItems = [
    {
      key: 'request',
      label: <span className="text-base font-bold">我申请的</span>,
    },
    {
      key: 'approve',
      label: <span className="text-base font-bold">我审核的</span>,
    },
  ]

  if (
    userInfo?.roles.includes('sa') ||
    userInfo?.roles.includes('qt_undertaker')
  ) {
    tabItems.unshift({
      key: 'all',
      label: <span className="text-base font-bold">所有请假</span>,
    })
  }

  useEffect(() => {
    handleTabChange(tabItems[0].key as 'request' | 'approve' | 'all')
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const getTableData = async (params: PageParams = pageParams) => {
    setTableLoading(true)
    const res = await queryLeaveList(params)
    if (res.code !== 200001) {
      setTableData([])
      setTableTotal(0)
    } else {
      setTableData(res.data.list)
      setTableTotal(res.data.pager.total)
    }
    setTableLoading(false)
  }

  const handleTabChange = async (key: 'request' | 'approve' | 'all') => {
    const params = {
      ...pageParams,
      leave_request_type: key === 'all' ? null : key,
      current: 1,
    }
    setPageParams(params)

    await getTableData(params)
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '集中学习' }, { title: '会议审批' }]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap justify="space-between">
          <Tabs
            defaultActiveKey={
              tabItems.find((item) => item.key === 'all') ? 'all' : 'request'
            }
            onChange={(key) =>
              handleTabChange(key as 'request' | 'approve' | 'all')
            }
            items={tabItems}
            style={{
              color: '#000000',
            }}
          />
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`/group/meeting`)}
          >
            会议列表
          </Button>
        </Flex>
        <CustomTable
          loading={tableLoading}
          columns={columns}
          dataSource={tableData}
          total={tableTotal}
          onChange={(page, pageSize) => {
            const params = { ...pageParams, current: page, pageSize: pageSize }
            setPageParams(params)
            getTableData(params)
          }}
        ></CustomTable>
      </div>
    </div>
  )
}

export default MeetingLeave
