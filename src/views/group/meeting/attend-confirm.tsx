import { ExclamationCircleOutlined } from '@ant-design/icons'
import { Button, Card, Input, message, Modal, Radio, Select } from 'antd'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ApiResponse, service } from '~/lib/service'
import { fetchMeetingCount } from '~/store/meeting'

interface AttendConfirmProps {
  meetingId: string
  userOptions?: { label: string; value: string }[]
}

interface AttendConfirmAction {
  id: string
  reason: string
  meeting_id: string
  full_name: string
  leave_type: string
  status: string
  cooperate_dept_operator_id: string
  cooperate_dept_operator: string
}

export const AttendConfirm = ({
  meetingId,
  userOptions = [],
}: AttendConfirmProps) => {
  const [originActions, setOriginActions] = useState<AttendConfirmAction[]>([])
  const [actions, setActions] = useState<AttendConfirmAction[]>([])
  const [saving, setSaving] = useState(false)
  const navigate = useNavigate()
  useEffect(() => {
    if (!meetingId) return
    const getData = async () => {
      const res = await service.get<ApiResponse<AttendConfirmAction[]>>(
        `/meetings/${meetingId}/confirms/action`
      )
      if (res.code !== 200001) return
      setActions((res.data || []).filter((item) => item.status === 'none'))
      setOriginActions(res.data || [])
    }
    getData()
  }, [meetingId])
  const updateActionValue = (id: string, key: string, value: string) => {
    setActions((prevActions) =>
      prevActions.map((action) =>
        action.id === id ? { ...action, [key]: value } : action
      )
    )
  }
  const handleConfirm = () => {
    for (let i = 0, len = actions.length; i < len; i++) {
      const action = actions[i]
      if (action.status === 'leave_request' && !action.leave_type) {
        message.warning(`请选择${action.full_name}的请假类型`)
        return
      }
      if (action.status === 'cooperate' && !action.cooperate_dept_operator_id) {
        message.warning(`请选择${action.full_name}的协同部门负责人`)
        return
      }
    }
    Modal.confirm({
      title: '确认提交吗？',
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        loading: saving,
      },
      onOk: async () => {
        setSaving(true)
        try {
          const items = actions.map((item) => {
            return {
              id: item.id,
              status: item.status,
              reason: item.reason,
              leave_type: item.leave_type,
              cooperate_dept_operator_id: item.cooperate_dept_operator_id,
              cooperate_dept_operator: item.cooperate_dept_operator,
            }
          })
          const res = await service.post<ApiResponse<null>>(
            `/meetings/${meetingId}/confirms/actions`,
            {
              items,
            }
          )
          if (res.code !== 200001) {
            return message.error(`参会确认失败：${res.message}`)
          }
          message.success('参会确认成功')
          navigate(`/group/meeting`)
          fetchMeetingCount()
        } finally {
          setSaving(false)
        }
      },
    })
  }
  if (actions.length === 0) return null
  if (originActions.every((item) => item.status !== 'none')) return null
  return (
    <Card bordered={false}>
      <div className="mb-4 text-base font-bold">参会确认</div>
      <div className="flex flex-col gap-4">
        {actions.map((item) => {
          const originItem = originActions.find((i) => i.id === item.id)
          if (originItem?.status !== 'none') return null
          return (
            <div key={item.id} className="flex flex-col gap-2">
              <div className="font-bold">参会人：{item.full_name}</div>
              <div>
                <Radio.Group
                  value={item.status}
                  className="flex gap-1"
                  onChange={(e) => {
                    updateActionValue(item.id, 'status', e.target.value)
                    if (e.target.value === 'present') {
                      updateActionValue(item.id, 'reason', '')
                    }
                  }}
                >
                  <Radio
                    value="present"
                    style={{ color: '#4caf50', fontWeight: 'bold' }}
                  >
                    参加
                  </Radio>
                  {item.leave_type !== '-' && (
                    <Radio
                      value="leave_request"
                      className="font-bold text-[#d32f2f]"
                    >
                      请假
                    </Radio>
                  )}
                  {/* {item.leave_type !== '-' && (
                    <Permission roles={['xt_head']} codes={[]}>
                      <Radio
                        value="cooperate"
                        className="font-bold text-[#4caf50]"
                      >
                        参加并协同
                      </Radio>
                    </Permission>
                  )} */}
                  {item.leave_type === '-' && (
                    <Radio value="not" className="font-bold">
                      不参加
                    </Radio>
                  )}
                </Radio.Group>
              </div>
              {item.status === 'leave_request' && (
                <div className="flex flex-col gap-2">
                  <p className="text-[#333333]">请假类型</p>
                  <Select
                    className="min-w-32"
                    value={item.leave_type}
                    onChange={(val) => {
                      updateActionValue(item.id, 'leave_type', val)
                    }}
                    options={[
                      { label: '出差', value: '出差' },
                      { label: '事假', value: '事假' },
                      { label: '病假', value: '病假' },
                      { label: '其他', value: '其他' },
                    ]}
                  />
                </div>
              )}
              {item.status === 'leave_request' && (
                <div className="flex flex-col gap-2">
                  <p className="text-[#333333]">请假原因</p>
                  <Input.TextArea
                    placeholder="请填写您的请假原因"
                    value={item.reason}
                    rows={3}
                    onChange={(e) => {
                      updateActionValue(item.id, 'reason', e.target.value)
                    }}
                  />
                </div>
              )}
              {item.status === 'cooperate' && (
                <div className="flex flex-col gap-2">
                  <p className="text-[#333333]">请指定协同部门承办人</p>
                  <Select
                    options={userOptions}
                    allowClear
                    placeholder="请选择协同部门负责人"
                    onChange={(_, option: any) => {
                      updateActionValue(
                        item.id,
                        'cooperate_dept_operator_id',
                        option.value
                      )
                      updateActionValue(
                        item.id,
                        'cooperate_dept_operator',
                        option.label
                      )
                    }}
                  />
                </div>
              )}
            </div>
          )
        })}
        <div className="flex justify-end">
          <Button
            color="primary"
            variant="solid"
            className="danger-btn"
            onClick={handleConfirm}
            loading={saving}
          >
            确认
          </Button>
        </div>
      </div>
    </Card>
  )
}
