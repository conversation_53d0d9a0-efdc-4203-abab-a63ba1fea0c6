import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Card,
  Descriptions,
  Timeline,
  Button,
  Radio,
  Input,
  Spin,
  Modal,
  Flex,
} from 'antd'
import { ExclamationCircleOutlined, FilePdfOutlined } from '@ant-design/icons'
import { useCallback, useEffect, useState } from 'react'
import { message } from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { ColumnsType } from 'antd/lib/table'
import { AttendConfirm } from './attend-confirm'
import {
  approveMeetingFlow,
  queryMeeting,
  queryMeetingConfirms,
  queryMeetingFlowAction,
  queryMeetingFlows,
} from '~/apis/group/meeting'
import { Meeting, MeetingResource } from '~/models/group/meeting'
import { Workflow } from '~/models/common/workflow'
import { MeetingConfirm } from '~/models/group/meeting/confirm'
import CustomTable, { DataType } from '~/components/table'
import { queryUserList } from '~/apis/user'
import { ApiResponse, service } from '~/lib/service'
import { fetchMeetingCount } from '~/store/meeting'
import { getMeetingName } from '~/lib/utils'

const MeetingDetailPage: React.FC = () => {
  const navigate = useNavigate()
  const { confirm } = Modal
  const { id } = useParams()
  const [loading, setLoading] = useState(false)
  const [detail, setDetail] = useState<Meeting>()
  const [statusName, setStatusName] = useState('')
  const [statusColor, setStatusColor] = useState('')
  const [approveConfirmLoading, setApproveConfirmLoading] = useState(false)
  const [selectedUserInfos, setSelectedUserInfos] = useState<any[]>([])

  // 获取列席人员、工作人员用户详情
  useEffect(() => {
    const userIds = Array.from(
      new Set([
        ...(detail?.observers?.map((item) => item.user_id) || []),
        ...(detail?.staff?.map((item) => item.user_id) || []),
        ...(detail?.supplements?.map((item) => item.user_id) || []),
      ])
    )
    if (userIds.length === 0) return
    const getData = async () => {
      try {
        const response = await service.post<ApiResponse<any[]>>(
          '/users/userOptionById',
          {
            ids: userIds,
          }
        )
        if (response.code == 200001) {
          setSelectedUserInfos(response.data || [])
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    getData()
  }, [detail])

  useEffect(() => {
    getDetail()
    getFlows()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id])

  const getDetail = async () => {
    const query = new URLSearchParams(window.location.search)
    const oaKey = query.get('key')
    setLoading(true)
    const res = await queryMeeting(id!, {
      key: oaKey,
    })
    setLoading(false)
    if (res.code !== 200001) {
      message.error(`会议详情获取失败：${res.message}`)
    } else {
      setDetail(res.data)
      setStatusName(getStatusName(res.data?.status || ''))
      getMaterials(res.data?.resources || [])

      if (
        res.data?.status === 'approving' ||
        res.data?.status === 'publishing'
      ) {
        getFlowAction()
      }
      getUserOptions()
      if (
        [
          'confirming',
          'confirm',
          'wait_publish',
          'publishing',
          'published',
          'publish rejected',
          'wait_start',
          'started',
          'ended',
          'archived',
        ].includes(res.data?.status as string)
      ) {
        getMeetingConfirms()
      }
    }
  }

  const getStatusName = (status: string) => {
    switch (status) {
      case 'created':
        return '已创建'
      case 'submitted':
        return '提交审核'
      case 'approving':
        return '审核中'
      case 'rejected':
        setStatusColor('text-[#d32f2f]')
        return '已驳回'
      case 'approved':
        setStatusColor('text-[#4caf50]')
        return '审核通过'
      case 'confirming':
        return '参会确认中'
      case 'confirm':
        return '参会确认'
      case 'wait_publish':
        return '待发布'
      case 'publishing':
        return '发布中'
      case 'published':
        return '已发布'
      case 'publish rejected':
        return '发布驳回'
      case 'wait_start':
        return '待开始'
      case 'started':
        return '进行中'
      case 'ended':
        return '已结束'
      case 'archived':
        return '已归档'
      default:
        return ''
    }
  }

  const [, setPreStudyMaterials] = useState<MeetingResource[]>([])
  const [transmitStudyMaterials, setTransmitStudyMaterials] = useState<
    MeetingResource[]
  >([])
  const [discussionMaterials, setDiscussionMaterials] = useState<
    MeetingResource[]
  >([])
  const [otherMaterials, setOtherMaterials] = useState<MeetingResource[]>([])
  const getMaterials = (resources: MeetingResource[]) => {
    resources.forEach((item: MeetingResource) => {
      switch (item.type) {
        case 'pre_study':
          setPreStudyMaterials((prev) => [...prev, item])
          break
        case 'transmit_study':
          setTransmitStudyMaterials((prev) => [...prev, item])
          break
        case 'discussion':
          setDiscussionMaterials((prev) => [...prev, item])
          break
        case 'other':
          setOtherMaterials((prev) => [...prev, item])
          break
      }
    })
  }

  const renderMaterialSection = (title: string, items: MeetingResource[]) => (
    <div className="mb-6">
      <div className="mb-4 text-base font-medium">{title}</div>
      {items.map((item, index) => (
        <div key={index} className="mb-4">
          {title !== '其他材料' && (
            <div className="flex items-center justify-between">
              <div className="flex w-full">
                <div
                  className="mb-2 text-sm"
                  style={{
                    width: '150px',
                    textAlign: 'left',
                    color: '#999999',
                  }}
                >
                  {title + (index + 1)}
                </div>
                <div
                  className="mb-2 flex-1 text-sm text-gray-600"
                  style={{ textAlign: 'left' }}
                >
                  {item.name || '--'}
                </div>
              </div>
            </div>
          )}

          {title === '交流研讨' && (
            <div className="flex items-center justify-between">
              <div className="flex w-full">
                <div
                  className="mb-2 text-sm"
                  style={{
                    width: '150px',
                    textAlign: 'left',
                    color: '#999999',
                  }}
                >
                  发言人
                </div>
                <div
                  className="mb-2 flex-1 text-sm text-gray-600"
                  style={{ textAlign: 'left' }}
                >
                  {item.speaker || '--'}
                </div>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex w-full">
              <div
                style={{
                  width: '150px',
                  textAlign: 'left',
                  color: '#999999',
                }}
                className="flex-shrink-0 flex-grow-0"
              >
                {title !== '其他材料' ? '相关材料' : `相关材料${index + 1}`}
              </div>
              {(item.link || item.file_id) && (
                <Flex
                  className="w-0 flex-1 border border-gray-200 p-2"
                  align="center"
                  justify="space-between"
                >
                  {item.name !== '其他材料' && (
                    <div className="flex w-0 flex-1">
                      <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                      <span className="flex-1 truncate text-sm">
                        {item.link
                          ? decodeURIComponent(item.link.split('/').pop() || '')
                          : '--'}
                      </span>
                    </div>
                  )}
                  <div className="flex-shrink-0 flex-grow-0">
                    {item.file_id && (
                      <Button
                        type="link"
                        size="large"
                        className="danger-btn-link text-sm"
                        onClick={() => onFilePreview(item.file_id as string)}
                      >
                        查看
                      </Button>
                    )}
                    {item.link && (
                      <Button
                        type="link"
                        size="large"
                        className="danger-btn-link text-sm"
                        onClick={() => {
                          window.open(item.link, '_blank')
                        }}
                      >
                        下载
                      </Button>
                    )}
                  </div>
                </Flex>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )

  const [flowAction, setFlowAction] = useState(false)
  const getFlowAction = () => {
    queryMeetingFlowAction(id!).then((res) => {
      if (res.code === 200001) {
        setFlowAction(res.data)
      }
    })
  }

  const [workFlows, setWorkFlows] = useState<Workflow[]>([])
  const getFlows = () => {
    queryMeetingFlows(id!).then((res) => {
      if (res.code !== 200001) {
        message.error(`工作流获取失败：${res.message}`)
      } else {
        setWorkFlows(res.data)
      }
    })
  }
  const getMeetingConfirmColumns = useCallback((isObserver?: boolean) => {
    const meetingConfirmColumns = [
      {
        title: isObserver ? '列席人员' : '参会人',
        dataIndex: 'full_name',
        key: 'full_name',
        align: 'center',
      },
      {
        title: '确认情况',
        dataIndex: 'status',
        key: 'status',
        align: 'center',
        render: (value) => {
          switch (value) {
            case 'present':
              return <p className="text-[#4caf50]">确认参会</p>
            case 'none':
              return <p className="text-[#4caf50]">未设置</p>
            case 'cooperate':
              return <p className="text-[#4caf50]">确认参会并协同</p>
            case 'leave_request':
              return <p className="text-[#d32f2f]">请假请求</p>
            case 'leave':
              return <p className="text-[#d32f2f]">请假</p>
            case 'not':
              return isObserver ? '已请假' : '不参加'
            case 'auto':
              return '自动确认'
          }
        },
      },
      {
        title: '请假类型',
        dataIndex: 'leave_type',
        key: 'leave_type',
        align: 'center',
        hidden: isObserver,
      },
    ] as ColumnsType<DataType>
    return meetingConfirmColumns
  }, [])

  const [meetingConfirms, setMeetingConfirms] = useState<MeetingConfirm[]>([])
  const getMeetingConfirms = () => {
    queryMeetingConfirms(id!).then((res) => {
      if (res.code !== 200001) {
        message.error(`参会确认情况获取失败：${res.message}`)
      } else {
        setMeetingConfirms(res.data)
      }
    })
  }

  const [approveResult, setApproveResult] = useState<'pass' | 'reject'>('pass')
  const [approveReason, setApproveReason] = useState('')
  const handleMeetingApprove = () => {
    confirm({
      title: `确定要审批${approveResult === 'pass' ? '通过' : '驳回'}该会议吗？`,
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        loading: approveConfirmLoading,
      },
      onOk() {
        setApproveConfirmLoading(true)
        approveMeetingFlow(id!, approveResult, approveReason).then((res) => {
          setApproveConfirmLoading(false)
          if (res.code !== 200001) {
            message.error(`审批失败：${res.message}`)
          } else {
            message.success('审批成功')
            navigate(`/group/meeting`)
          }
          fetchMeetingCount()
        })
      },
    })
  }

  const [userOptions, setUserOptions] = useState<any[]>([])
  const [attachedLearnResources, setAttachedLearnResources] = useState<any[]>(
    []
  )

  useEffect(() => {
    const getLearnResources = async () => {
      const attachedLearnId = (detail?.resources || []).find(
        (item: any) => item.learn_id
      )?.learn_id
      if (!attachedLearnId) return setAttachedLearnResources([])
      const res = await service.get<ApiResponse<any>>(
        `/learns/${attachedLearnId}/resources`,
        {
          params: {
            current: 1,
            pageSize: 9999,
          },
        }
      )
      if (res.code !== 200001) return setAttachedLearnResources([])
      setAttachedLearnResources(res.data.list || [])
    }
    getLearnResources()
  }, [detail])
  const getUserOptions = async () => {
    const res = await queryUserList()
    if (res.code !== 200001) {
      message.error(`协同部门承办人获取失败：${res.message}`)
    } else {
      const options = res.data.map((item) => {
        return { value: item.id, label: item.full_name }
      })
      setUserOptions(options)
    }
  }

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const onFileDownload = async (fileId: string, name: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    const fileName = decodeURIComponent((name || '').split('/').pop() || 'file')
    try {
      const res = await service.get<Blob>(`/files/${fileId}/download`, {
        responseType: 'blob',
      })
      const blobUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      message.destroy()
      message.success('文件下载成功')
    } catch (error: any) {
      message.destroy()
      message.open({
        type: 'error',
        content: error?.message,
        duration: 2,
      })
    }
  }

  const renderPreStudyFileItem = (item: any) => {
    if (item.mode === '电子链接') {
      return (
        <div className="flex w-0 flex-1 gap-2">
          <a href={item.link} target="_blank">
            查看
          </a>
        </div>
      )
    }
    if (item.mode === '电子文档') {
      return (
        <div className="flex w-0 flex-1 gap-2">
          <a onClick={() => onFilePreview(item.file_id)}>查看</a>
          <a onClick={() => onFileDownload(item.file_id, item.name)}>下载</a>
        </div>
      )
    }
    if (item.mode === '纸质文档') return '--'
    if (item.link || item.file_id) {
      return (
        <Flex
          className="w-0 flex-1 border border-gray-200 p-2"
          align="center"
          justify="space-between"
        >
          {item.name !== '其他材料' && (
            <div className="flex w-0 flex-1">
              <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
              <span className="flex-1 truncate text-sm">
                {item.link
                  ? decodeURIComponent(item.link.split('/').pop() || '')
                  : '--'}
              </span>
            </div>
          )}
          <div className="flex-shrink-0 flex-grow-0">
            {item.file_id && (
              <Button
                type="link"
                size="large"
                className="danger-btn-link text-sm"
                onClick={() => onFilePreview(item.file_id as string)}
              >
                查看
              </Button>
            )}
            {item.link && (
              <Button
                type="link"
                size="large"
                className="danger-btn-link text-sm"
                onClick={() => {
                  window.open(item.link, '_blank')
                }}
              >
                下载
              </Button>
            )}
          </div>
        </Flex>
      )
    }
  }

  const renderPreStudyInfo = (detail: any) => {
    const resources = (detail?.resources || []).filter(
      (item: any) => item.type === 'pre_study'
    )
    let preStudyData: any[] = []
    if (
      resources.find((item: any) => item.learn_id && item.type === 'pre_study')
    ) {
      preStudyData = [...preStudyData, ...attachedLearnResources]
    }
    const otherPreStudy = resources.filter((item: any) => !item.learn_id)
    if (otherPreStudy && otherPreStudy.length) {
      preStudyData = [...preStudyData, ...otherPreStudy]
    }
    return (
      <div className="mb-6">
        <div className="mb-4 text-base font-medium">会前自学</div>
        {preStudyData.map((item: any, index: number) => {
          return (
            <div key={item.id} className="mb-4">
              <div className="flex items-center justify-between">
                <div className="flex w-full">
                  <div
                    className="mb-2 text-sm"
                    style={{
                      width: '150px',
                      textAlign: 'left',
                      color: '#999999',
                    }}
                  >
                    {`会前自学${index + 1}`}
                  </div>
                  <div
                    className="mb-2 flex-1 text-sm text-gray-600"
                    style={{ textAlign: 'left' }}
                  >
                    {item.content || item.name || '--'}
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex w-full">
                  <div
                    style={{
                      width: '150px',
                      textAlign: 'left',
                      color: '#999999',
                    }}
                    className="flex-shrink-0 flex-grow-0"
                  >
                    {`相关材料${index + 1}`}
                  </div>
                  {renderPreStudyFileItem(item)}
                  {/* {item.link ? (
                    <Flex
                      className="w-0 flex-1 border border-gray-200 p-2"
                      align="center"
                      justify="space-between"
                    >
                      <div className="flex w-0 flex-1">
                        <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                        <span className="flex-1 truncate text-sm">
                          {item.link
                            ? decodeURIComponent(
                                item.link.split('/').pop() || ''
                              )
                            : '--'}
                        </span>
                      </div>
                      <Button
                        type="link"
                        size="large"
                        className="danger-btn-link flex-shrink-0 flex-grow-0 text-sm"
                        onClick={() => {
                          window.open(item.link, '_blank')
                        }}
                      >
                        下载
                      </Button>
                    </Flex>
                  ) : (
                    item.file_id && <FileDetail id={item.file_id} />
                  )} */}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  if (loading)
    return (
      <div className="flex h-full items-center justify-center">
        <Spin spinning />
      </div>
    )

  return (
    <div className="flex h-full flex-col gap-4">
      <Breadcrumb
        separator=">"
        className="flex-shrink-0 flex-grow-0"
        items={[
          {
            title: '集中学习',
            href: '#',
            onClick: () => navigate(`/group/meeting`),
          },
          { title: '详情页' },
        ]}
      />

      <div className="grid h-0 flex-1 grid-cols-2 gap-4">
        <div className="grid-rows-1 overflow-y-auto">
          <Card bordered={false}>
            <div className="mb-6">
              <div className="text-base font-bold">
                {getMeetingName(detail as any)}
              </div>
              <div className="mt-2 text-sm text-gray-400">
                <div>创建时间：{detail?.created_at}</div>
                <div>
                  状态：<span className={statusColor}>{statusName}</span>
                </div>
                <div>下一步：{detail?.next}</div>
              </div>
              <hr className="mt-4 border-t border-gray-200" />
            </div>

            <div className="mb-6">
              <div className="mb-4 text-base font-bold">会议信息</div>
              <Descriptions
                column={1}
                labelStyle={{ width: '120px', textAlign: 'right' }}
              >
                <Descriptions.Item label="是否计划内学习">
                  {detail?.is_plan}
                </Descriptions.Item>
                <Descriptions.Item label="会议开始时间">
                  {detail?.start_at &&
                    dayjs(detail?.start_at).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="会议结束时间">
                  {detail?.end_at &&
                    dayjs(detail?.end_at).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="会议地点">
                  {detail?.room}
                </Descriptions.Item>

                <Descriptions.Item label="参会人员">
                  {detail?.members?.map((it, ind, arr) => (
                    <div key={ind}>
                      {it.full_name}
                      {ind !== arr.length - 1 ? '、' : ''}
                    </div>
                  ))}
                </Descriptions.Item>

                <Descriptions.Item label="列席人员">
                  {detail?.observers
                    ?.map((item) => {
                      const userObj = selectedUserInfos.find(
                        (opt) => opt.id === item.user_id
                      )
                      if (userObj) {
                        return userObj.full_name
                      }
                      return null
                    })
                    .filter((item) => !!item)
                    .join('、')}
                </Descriptions.Item>
                <Descriptions.Item label="工作人员">
                  {detail?.staff
                    ?.map((item: any) => {
                      const userObj = selectedUserInfos.find(
                        (opt) => opt.id === item.user_id
                      )
                      if (userObj) {
                        return userObj.full_name
                      }
                      return null
                    })
                    .filter((item) => !!item)
                    .join('、')}
                </Descriptions.Item>
                <Descriptions.Item label="学习主题">
                  {detail?.topic}
                </Descriptions.Item>
              </Descriptions>
              <hr className="mt-4 border-t border-gray-200" />
            </div>

            {/* <div className="mb-6">
              <div className="mb-4 text-base font-bold">审核领导</div>
              <Descriptions
                column={1}
                labelStyle={{ width: '120px', textAlign: 'right' }}
              >
                <Descriptions.Item label="党委书记">
                  {detail?.implement?.officer}
                </Descriptions.Item>
                <Descriptions.Item label="分管领导">
                  {detail?.implement?.leader}
                </Descriptions.Item>
                <Descriptions.Item label="牵头部门负责人">
                  {detail?.implement?.dept_leader}
                </Descriptions.Item>
                <Descriptions.Item label="牵头部门审核人">
                  {detail?.implement?.dept_auditor}
                </Descriptions.Item>
              </Descriptions>
              <hr className="mt-4 border-t border-gray-200" />
            </div> */}

            <div className="mb-5 text-base font-bold">会议材料</div>
            {renderPreStudyInfo(detail)}
            {renderMaterialSection('传达学习', transmitStudyMaterials)}
            {renderMaterialSection('交流研讨', discussionMaterials)}
            {renderMaterialSection('其他材料', otherMaterials)}
            <div className="mb-6">
              <div className="mb-4 text-base font-medium">会后补录</div>
              {detail?.after?.brief_name && (
                <div className="mb-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div className="w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                      会议简报
                    </div>
                    <div className="flex-1 text-left text-sm text-gray-600">
                      {detail?.after?.brief_name}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <div className="mb-2 w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                      相关材料
                    </div>
                    <div className="flex flex-1 items-center justify-between border border-gray-200 p-2 text-left text-sm text-gray-600">
                      <div className="flex w-0 flex-1">
                        <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                        <span className="flex-1 truncate text-sm">
                          {detail?.after?.brief_name}
                        </span>
                      </div>
                      <div className="flex-shrink-0 flex-grow-0">
                        {detail?.after?.brief_file_id && (
                          <Button
                            type="link"
                            size="large"
                            className="danger-btn-link text-sm"
                            onClick={() =>
                              onFilePreview(
                                detail?.after?.brief_file_id as string
                              )
                            }
                          >
                            查看
                          </Button>
                        )}
                        {detail?.after?.brief_link && (
                          <Button
                            type="link"
                            size="large"
                            className="danger-btn-link text-sm"
                            onClick={() => {
                              window.open(detail?.after?.brief_link, '_blank')
                            }}
                          >
                            下载
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {detail?.after?.summary_name && (
                <div className="mb-4">
                  <div className="mb-2 flex items-center justify-between">
                    <div className="w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                      会议纪要
                    </div>
                    <div className="flex-1 text-left text-sm text-gray-600">
                      {detail?.after?.summary_name}
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <div className="mb-2 w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                      相关材料
                    </div>
                    <div className="flex flex-1 items-center justify-between border border-gray-200 p-2 text-left text-sm text-gray-600">
                      <div className="flex w-0 flex-1">
                        <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                        <span className="flex-1 truncate text-sm">
                          {detail?.after?.summary_name}
                        </span>
                      </div>
                      <div className="flex-shrink-0 flex-grow-0">
                        {detail?.after?.summary_file_id && (
                          <Button
                            type="link"
                            size="large"
                            className="danger-btn-link text-sm"
                            onClick={() =>
                              onFilePreview(
                                detail?.after?.summary_file_id as string
                              )
                            }
                          >
                            查看
                          </Button>
                        )}
                        {detail?.after?.summary_link && (
                          <Button
                            type="link"
                            size="large"
                            className="danger-btn-link text-sm"
                            onClick={() => {
                              window.open(detail?.after?.summary_link, '_blank')
                            }}
                          >
                            下载
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {(detail?.supplements || []).map((item, index) => {
                return (
                  <div className="mb-4" key={item.id}>
                    <div className="mb-2 flex items-center justify-between">
                      <div className="w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                        请假人{index + 1}
                      </div>
                      <div className="flex-1 text-left text-sm text-gray-600">
                        {(selectedUserInfos || []).find(
                          (user) => user.id === item.user_id
                        )?.full_name || '--'}
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <div className="w-[150px] flex-shrink-0 flex-grow-0 text-left text-sm text-[#999]">
                        补学材料{index + 1}
                      </div>
                      <div className="flex flex-1 items-center justify-between border border-gray-200 p-2 text-left text-sm text-gray-600">
                        <div className="flex w-0 flex-1">
                          <FilePdfOutlined className="mr-2 flex-shrink-0 flex-grow-0 text-red-500" />
                          <span className="flex-1 truncate text-sm">
                            {item.name}
                          </span>
                        </div>
                        <div className="flex-shrink-0 flex-grow-0">
                          {item.file_id && (
                            <Button
                              type="link"
                              size="large"
                              className="danger-btn-link text-sm"
                              onClick={() =>
                                onFilePreview(item.file_id as string)
                              }
                            >
                              查看
                            </Button>
                          )}
                          {item.link && (
                            <Button
                              type="link"
                              size="large"
                              className="danger-btn-link text-sm"
                              onClick={() => {
                                window.open(item.link, '_blank')
                              }}
                            >
                              下载
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </Card>
        </div>
        <div className="grid-rows-1 overflow-y-auto">
          {flowAction && (
            <Card bordered={false} className="mb-4">
              <div className="mb-4 text-base font-bold">审批批示</div>
              <Radio.Group
                value={approveResult}
                className="flex gap-8"
                onChange={(e) => setApproveResult(e.target.value)}
              >
                <Radio
                  value="pass"
                  style={{ color: '#4caf50', fontWeight: 'bold' }}
                >
                  通过
                </Radio>
                <Radio
                  value="reject"
                  style={{
                    color: '#d32f2f',
                    fontWeight: 'bold',
                  }}
                >
                  驳回
                </Radio>
              </Radio.Group>
              <div className="mt-2">
                <p className="my-2 text-[#333333]">
                  {approveResult === 'pass' ? '批示意见' : '驳回原因'}
                </p>
                <Input.TextArea
                  value={approveReason}
                  placeholder={`请填写您的${approveResult === 'pass' ? '批示意见' : '驳回原因'}`}
                  onChange={(e) => setApproveReason(e.target.value)}
                  rows={3}
                />
              </div>
              <div className="mt-4 flex justify-end">
                <Button
                  color="primary"
                  variant="solid"
                  className="danger-btn"
                  onClick={handleMeetingApprove}
                  loading={approveConfirmLoading}
                >
                  确认
                </Button>
              </div>
            </Card>
          )}
          {detail?.status === 'confirming' && (
            <AttendConfirm meetingId={id!} userOptions={userOptions} />
          )}

          {[
            'confirming',
            'confirm',
            'wait_publish',
            'publishing',
            'published',
            'publish rejected',
            'wait_start',
            'started',
            'ended',
            'archived',
          ].includes(detail?.status as string) && (
            <Card bordered={false} className="mb-4">
              <div className="mb-4 text-base font-bold">参会确认情况</div>
              <div className="mb-2">
                <CustomTable
                  columns={getMeetingConfirmColumns()}
                  dataSource={(meetingConfirms || []).filter(
                    (item) => item.leave_type !== '-'
                  )}
                  pagination={false}
                  className="h-full overflow-hidden"
                ></CustomTable>
              </div>
              <CustomTable
                columns={getMeetingConfirmColumns(true)}
                dataSource={(meetingConfirms || []).filter(
                  (item) => item.leave_type === '-'
                )}
                pagination={false}
                className="h-full overflow-hidden"
              ></CustomTable>
            </Card>
          )}

          <Card bordered={false}>
            <div className="mb-4 text-base font-bold">工作流程</div>
            <Timeline
              items={workFlows.map((item) => ({
                children: (
                  <Flex vertical gap="small" className="text-sm text-[#999999]">
                    {item.node === 'confirm_start' && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          发起参会确认
                        </p>
                        <p>操作人：{item.operator}</p>
                        <p>操作时间：{item.created_at}</p>
                      </>
                    )}
                    {item.node === 'end' && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          全部审核通过
                        </p>
                        <p>提交时间：{item.created_at}</p>
                      </>
                    )}
                    {(item.node === 'officer' ||
                      item.node === 'leader' ||
                      item.node === 'dept_leader' ||
                      item.node === 'dept_auditor') && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          {item.result === 'pass' && '审批通过'}
                          {item.result === 'reject' && '审批驳回'}
                          {item.result === 'pending' && '审批中'}
                          {item.result === 'cancel' && '取消'}
                        </p>
                        <p>审批人：{item.approver}</p>
                        {item.result !== 'reject' && (
                          <p>审批意见：{item.reason}</p>
                        )}
                        {item.result === 'reject' && (
                          <p className="text-[#d32f2f]">
                            驳回原因：{item.reason}
                          </p>
                        )}
                        <p>审批时间：{item.approved_at}</p>
                      </>
                    )}

                    {item.node === 'release' && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          发布会议
                        </p>
                        <p>操作人：{item.operator}</p>
                        <p>操作时间：{item.created_at}</p>
                      </>
                    )}

                    {item.node === 'start' && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          创建会议
                        </p>
                        <p>操作人：{item.operator}</p>
                        <p>操作时间：{item.created_at}</p>
                      </>
                    )}

                    {item.node === 'all_confirms' && (
                      <>
                        <p className="text-base font-semibold text-[#333333]">
                          参会确认全部完成
                        </p>
                        <p>操作时间：{item.created_at}</p>
                      </>
                    )}
                  </Flex>
                ),
              }))}
            />
          </Card>
        </div>
      </div>
    </div>
  )
}

export default MeetingDetailPage
