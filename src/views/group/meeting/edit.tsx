import {
  ExclamationCircleOutlined,
  MinusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import {
  Breadc<PERSON>b,
  Button,
  Col,
  DatePicker,
  Flex,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  message,
} from 'antd'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import FileUpload from '~/components/upload-file'
import { ApiResponse, service } from '~/lib/service'
import { fetchMeetingCount } from '~/store/meeting'
import { startAttendMeetingAction } from '~/apis/group/meeting'
import { useAuthStore } from '~/store/auth'
import { isNil } from '~/lib/utils'

interface Learn {
  key: string
  company_id: string
  created_at: string
  creator: string
  creator_id: string
  deleted_at: string
  dept_id: string
  id: string
  name: string
  updated_at: string
  full_name: string
  roles: []
}
const Meeting: React.FC = () => {
  const [meetingInfoForm] = Form.useForm()
  const [optionsFromAPI, setOptionsFromAPI] = useState<any[]>([])

  const attachedLearnId = Form.useWatch('attachedLearnId', meetingInfoForm)
  // const [selfStudyBeforeTheMeeting, setSelfStudyBeforeTheMeeting] = useState<
  //   any[]
  // >([])
  const [observersOption, setObserversOption] = useState<any[]>([])
  const [userOptionsV2, setUserOptionsV2] = useState<any[]>([])
  const [selectedMembers, setSelectedMembers] = useState<any[]>([])
  const [zxType, setZXType] = useState<'attach' | 'manual'>('attach')
  const [learnOptions, setlearnOptions] = useState<any[]>([])
  const [attachedLearnResources, setAttachedLearnResources] = useState<any[]>(
    []
  )

  const [attachedLearnDetailLoading, setAttachedLearnDetailLoading] =
    useState(false)
  const [loading, setLoading] = useState(false)
  const [saving1, setSaving1] = useState(false)
  const [saving2, setSaving2] = useState(false)
  const [saving3, setSaving3] = useState(false)
  const [releasePublishing, setReleasePublishing] = useState(false)
  const [meetingDetail, setMeetingDetail] = useState<any>({})
  const [selectedUserInfos, setSelectedUserInfos] = useState<any[]>([])

  const searchParams = new URLSearchParams(window.location.search)

  useEffect(() => {
    const userIds = Array.from(
      new Set(
        [
          meetingDetail?.implement?.cooperate_dept_leader_id,
          meetingDetail?.implement?.dept_auditor_id,
          meetingDetail?.implement?.dept_leader_id,
          meetingDetail?.implement?.leader_id,
          meetingDetail?.implement?.officer_id,
          ...(meetingDetail?.members?.map((item: any) => item.user_id) || []),
          ...(meetingDetail?.staff?.map((item: any) => item.user_id) || []),
          ...(meetingDetail?.observers?.map((item: any) => item.user_id) || []),
          ...(meetingDetail?.resources?.map((item: any) => item.speaker_id) ||
            []),
          ...(meetingDetail?.supplements?.map((item: any) => item.user_id) ||
            []),
        ].filter((id: string) => !!id)
      )
    )
    if (userIds.length === 0) return
    const getData = async () => {
      try {
        const response = await service.post<ApiResponse<any[]>>(
          '/users/userOptionByIdV2',
          {
            ids: userIds,
          }
        )
        if (response.code == 200001) {
          setSelectedUserInfos(
            (response.data || []).map((item: any) => {
              return {
                value: item.id,
                label: item.full_name,
              }
            })
          )
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    getData()
  }, [meetingDetail])

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const response = await service.get<ApiResponse<Learn[]>>('/users')
        if (response.code == 200001) {
          const processedItems = response.data.map((item) => {
            const newItem = {
              value: item.id,
              label: item.full_name,
              roles: item.roles,
            }
            // 在这里处理item
            return newItem
          })
          setOptionsFromAPI(processedItems)
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const response =
          await service.get<ApiResponse<Learn[]>>('/users/userOption')
        if (response.code == 200001) {
          const processedItems = response.data.map((item) => {
            const newItem = { value: item.id, label: item.full_name || item.id }
            // 在这里处理item
            return newItem
          })
          setObserversOption(processedItems)
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const response = await service.get<ApiResponse<any[]>>(
          '/users/userOptionV2'
        )
        if (response.code == 200001) {
          setUserOptionsV2(response.data || [])
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])

  useEffect(() => {
    const fetchOptions = async () => {
      const res = await service.get<
        ApiResponse<{
          list: any[]
        }>
      >('/resources/byCompanyId', {
        params: {
          pageSize: 9999,
          current: 1,
          conttent: '',
        },
      })
      if (res.code !== 200001) return
      const data = res.data.list || []
      setlearnOptions(data)
    }
    fetchOptions()
  }, [])
  useEffect(() => {
    const getLearnResources = async () => {
      if (!attachedLearnId) return setAttachedLearnResources([])
      setAttachedLearnDetailLoading(true)
      try {
        const res = await service.get<ApiResponse<any>>(
          `/learns/${attachedLearnId}/resources`,
          {
            params: {
              current: 1,
              pageSize: 9999,
            },
          }
        )
        if (res.code !== 200001) return setAttachedLearnResources([])
        setAttachedLearnResources(res.data.list || [])
      } finally {
        setAttachedLearnDetailLoading(false)
      }
    }
    getLearnResources()
  }, [attachedLearnId])
  useEffect(() => {
    meetingInfoForm.setFieldValue('attachedLearnId', '')
    // meetingInfoForm.setFieldValue('selfStudyList', [])
    // setAttachedLearnResources([])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [zxType])
  const location = useLocation()
  const navigate = useNavigate()
  const routeParams = useParams()
  const [currentTitle, setCurrentTitle] = useState('创建会议') // 1:创建会议 2:编辑会议 3:发布会议 4:会议补录
  const [currentType, setCurrentType] = useState(1)
  const [attendMeetingLoading, setAttendMeetingLoading] = useState(false)
  const { userInfo } = useAuthStore()

  useEffect(() => {
    const query = new URLSearchParams(window.location.search)
    const oaKey = query.get('key')
    if (location.pathname.indexOf('/update/') > -1) {
      setCurrentTitle('编辑会议')
      setCurrentType(2)
      const meetingId = location.pathname.split('/update/')[1]
      setLoading(true)
      service
        .get(`/meetings/${meetingId}`, {
          params: {
            key: oaKey,
          },
        })
        .then((res: any) => {
          setLoading(false)
          setMeetingDetail(res.data || {})
          const attachedLearn = (res?.data?.resources || []).find(
            (item: any) => item.learn_id
          )
          const manualLearn = (res?.data?.resources || []).filter(
            (item: any) => {
              return !item.learn_id && item.type === 'pre_study'
            }
          )
          if (manualLearn && manualLearn.length) setZXType('manual')
          meetingInfoForm.setFieldsValue({
            ...res.data,
            start_at: dayjs(res.data.start_at),
            end_at: dayjs(res.data.end_at),
            year: res.data.year ? dayjs(`${res.data.year}-01-01`) : null,
            membersData: res.data.members.map((member: any) => member.user_id),
            observers: res.data.observers.map(
              (observer: any) => observer.user_id
            ),
            staff: res.data.staff.map((item: any) => item.user_id),
            officer_value: res.data.implement.officer_id,
            leader_value: res.data.implement.leader_id,
            dept_leader_value: res.data.implement.dept_leader_id,
            dept_auditor_value: res.data.implement.dept_auditor_id,
            cooperate_dept_leader_value:
              res.data.implement.cooperate_dept_leader_id,
            selfStudyList: [...Feedback(manualLearn, 'pre_study')],
            conveyStudyList: [
              ...Feedback(res.data.resources, 'transmit_study'),
            ],
            exchangeStudyList: [...Feedback(res.data.resources, 'discussion')],
            other: [...Feedback(res.data.resources, 'other')],
            attachedLearnId: attachedLearn?.learn_id,
          })
          setSelectedMembers(
            res.data.members.map((member: any) => ({
              name: member.full_name,
              id: member.id,
            }))
          )
        })
    }
    if (location.pathname.indexOf('/release/') > -1) {
      setCurrentTitle('发布会议')
      setCurrentType(3)
      const meetingId = location.pathname.split('/release/')[1]
      setLoading(true)
      service
        .get(`/meetings/${meetingId}`, {
          params: {
            key: oaKey,
          },
        })
        .then((res: any) => {
          setLoading(false)
          setMeetingDetail(res.data || {})
          const attachedLearn = (res?.data?.resources || []).find(
            (item: any) => item.learn_id
          )
          const manualLearn = (res?.data?.resources || []).filter(
            (item: any) => {
              return !item.learn_id && item.type === 'pre_study'
            }
          )
          if (manualLearn && manualLearn.length) setZXType('manual')
          meetingInfoForm.setFieldsValue({
            ...res.data,
            start_at: dayjs(res.data.start_at),
            end_at: dayjs(res.data.end_at),
            year: res.data.year ? dayjs(`${res.data.year}-01-01`) : null,
            membersData: res.data.members.map((member: any) => member.user_id),
            observers: res.data.observers.map(
              (observer: any) => observer.user_id
            ),
            staff: res.data.staff.map((item: any) => item.user_id),
            officer_value: res.data.implement.officer_id,
            leader_value: res.data.implement.leader_id,
            dept_leader_value: res.data.implement.dept_leader_id,
            dept_auditor_value: res.data.implement.dept_auditor_id,
            cooperate_dept_leader_value:
              res.data.implement.cooperate_dept_leader_id,
            selfStudyList: [...Feedback(manualLearn, 'pre_study')],
            conveyStudyList: [
              ...Feedback(res.data.resources, 'transmit_study'),
            ],
            exchangeStudyList: [...Feedback(res.data.resources, 'discussion')],
            other: [...Feedback(res.data.resources, 'other')],
            attachedLearnId: attachedLearn?.learn_id,
          })
          setSelectedMembers(
            res.data.members.map((member: any) => ({
              name: member.full_name,
              id: member.id,
            }))
          )
        })
    }
    if (location.pathname.indexOf('/supplement/') > -1) {
      if (searchParams.get('type') === 'bcxtcl') {
        setCurrentTitle('编辑会议')
      } else {
        setCurrentTitle('会后上传')
      }
      setCurrentType(4)
      setLoading(true)
      service
        .get(`/meetings/${routeParams?.id}`, {
          params: {
            key: oaKey,
          },
        })
        .then((res: any) => {
          setLoading(false)
          setMeetingDetail(res.data || {})
          const attachedLearn = (res?.data?.resources || []).find(
            (item: any) => item.learn_id
          )
          const manualLearn = (res?.data?.resources || []).filter(
            (item: any) => {
              return !item.learn_id && item.type === 'pre_study'
            }
          )
          if (manualLearn && manualLearn.length) setZXType('manual')
          meetingInfoForm.setFieldsValue({
            ...res.data,
            start_at: dayjs(res.data.start_at),
            end_at: dayjs(res.data.end_at),
            year: res.data.year ? dayjs(`${res.data.year}-01-01`) : null,
            membersData: res.data.members.map((member: any) => member.user_id),
            observers: res.data.observers.map(
              (observer: any) => observer.user_id
            ),
            staff: res.data.staff.map((item: any) => item.user_id),
            officer_value: res.data.implement.officer_id,
            leader_value: res.data.implement.leader_id,
            dept_leader_value: res.data.implement.dept_leader_id,
            dept_auditor_value: res.data.implement.dept_auditor_id,
            cooperate_dept_leader_value:
              res.data.implement.cooperate_dept_leader_id,
            selfStudyList: [...Feedback(manualLearn, 'pre_study')],
            conveyStudyList: [
              ...Feedback(res.data.resources, 'transmit_study'),
            ],
            exchangeStudyList: [...Feedback(res.data.resources, 'discussion')],
            other: [...Feedback(res.data.resources, 'other')],
            attachedLearnId: attachedLearn?.learn_id,
            // 会议简报
            hyjbInfo: res.data?.after?.brief_name
              ? [
                  {
                    name: res.data?.after?.brief_name,
                    url: res.data?.after?.brief_link,
                    id: res.data?.after?.brief_file_id,
                  },
                ]
              : [],
            // 会议纪要
            hyjyInfo: res.data?.after?.summary_name
              ? [
                  {
                    name: res.data?.after?.summary_name,
                    url: res.data?.after?.summary_link,
                    id: res.data?.after?.summary_file_id,
                  },
                ]
              : [],
            // 补学材料
            bxclInfoList: res.data?.supplements.map((item: any) => {
              const ret: { files?: any[]; user_id: string } = {
                user_id: item.user_id,
              }
              if (item.link || item.name) {
                ret.files = [
                  { url: item.link, name: item.name, id: item.file_id },
                ]
              }
              return ret
            }),
          })
          setSelectedMembers(
            res.data.members.map((member: any) => ({
              name: member.full_name,
              id: member.id,
            }))
          )
        })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const Feedback = (data: any, type: string) => {
    const list: any = []
    for (let i = 0; i < data.length; i++) {
      if (data[i].type == type) {
        const a: any = {
          file_id: data[i].file_id,
          content: data[i].name,
          speak: data[i].speaker,
          speaker_id: data[i].speaker_id,
          url: data[i].link,
        }
        if (data[i].link || data[i].file_id) {
          a.files = [
            {
              url: data[i].link,
              file_id: data[i].file_id,
            },
          ]
        }
        list.push(a)
      }
    }
    return list
  }

  const onCreatePlanFinish = (values: any) => {
    console.log(values)
  }

  const onCreatePlanFinishFailed = () => {}

  const timeFormat = (date: any) => {
    const formattedStartAt = new Date(date)
      .toISOString()
      .slice(0, 19)
      .replace('T', ' ')
    return formattedStartAt
  }

  const recursion = (id: any, type: any) => {
    if (type == 'name') {
      return optionsFromAPI.find((option) => option.value === id)?.label || null
    } else {
      return optionsFromAPI.find((option) => option.value === id)?.value || null
    }
  }

  const processingData = (data: any) => {
    const updataList = []
    for (let i = 0; i < data.selfStudyList?.length; i++) {
      const list = {
        type: 'pre_study',
        name: data.selfStudyList[i]?.content,
        link: data.selfStudyList[i]?.files?.[0]?.url,
        file_id:
          data.selfStudyList[i]?.files?.[0]?.id ||
          data.selfStudyList[i]?.file_id,
        speaker: '',
      }
      updataList.push(list)
    }
    if (attachedLearnId) {
      const learn = learnOptions.find((item) => item.id === attachedLearnId)
      if (learn) {
        updataList.push({
          type: 'pre_study',
          learn_id: learn.id,
          learn_name: learn.name,
        })
      }
    }
    for (let i = 0; i < data.conveyStudyList?.length; i++) {
      const list = {
        type: 'transmit_study',
        name: data.conveyStudyList[i]?.content,
        link: data.conveyStudyList[i]?.files?.[0]?.url,
        file_id:
          data.conveyStudyList[i]?.files?.[0]?.id ||
          data.conveyStudyList[i]?.file_id,
        speaker: '',
      }
      updataList.push(list)
    }
    for (let i = 0; i < data.exchangeStudyList?.length; i++) {
      const speak = {
        name:
          data.exchangeStudyList[i]?.speak?.label ||
          data.exchangeStudyList[i]?.speaker ||
          data.exchangeStudyList[i]?.speak,
        id:
          data.exchangeStudyList[i]?.speak?.value ||
          data.exchangeStudyList[i]?.speaker_id,
      }
      const list = {
        type: 'discussion',
        name: data.exchangeStudyList[i]?.content,
        link: data.exchangeStudyList[i]?.files?.[0].url,
        file_id:
          data.exchangeStudyList[i]?.files?.[0].id ||
          data.exchangeStudyList[i]?.file_id,
        speaker: speak?.name,
        speaker_id: speak?.id,
      }
      updataList.push(list)
    }
    for (let i = 0; i < data.other?.length; i++) {
      const list = {
        type: 'other',
        name: '',
        link: data.other[i]?.url,
        file_id: data.other[i]?.id || data.other[i]?.file_id,
        speaker: '',
      }
      updataList.push(list)
    }
    return updataList
  }

  const handleSaveAndApply = async (type: string) => {
    try {
      let values: any = {}
      if (type === '2') {
        values = meetingInfoForm.getFieldsValue()
        values.is_draft = meetingDetail?.is_draft
        if (!values.name) return message.warning('请输入会议名称')
        if (!values.year) return message.warning('请选择会议年度')
        if (!values.count) return message.warning('请输入会议次数')
        if (isNil(values.is_draft)) values.is_draft = true
      } else {
        values = await meetingInfoForm.validateFields()
        values.is_draft = false
      }
      values.year = dayjs(values.year).get('year')
      if (values.start_at) {
        values.start_at = timeFormat(values.start_at)
      }
      if (values.end_at) {
        values.end_at = timeFormat(values.end_at)
      }
      values.implement = {
        officer: recursion(values.officer_value, 'name'),
        officer_id: recursion(values.officer_value, 'id'),
        leader: recursion(values.leader_value, 'name'),
        leader_id: recursion(values.leader_value, 'id'),
        dept_leader: recursion(values.dept_leader_value, 'name'),
        dept_leader_id: recursion(values.dept_leader_value, 'id'),
        dept_auditor: recursion(values.dept_auditor_value, 'name'),
        dept_auditor_id: recursion(values.dept_auditor_value, 'id'),
        cooperate_dept_leader: recursion(
          values.cooperate_dept_leader_value,
          'name'
        ),
        cooperate_dept_leader_id: recursion(
          values.cooperate_dept_leader_value,
          'id'
        ),
      }
      values.members = (values.membersData || []).map((item: any) => {
        return { user_id: item }
      })
      values.observers = (values.observers || []).map((item: any) => {
        return { user_id: item }
      })
      values.staff = (values.staff || []).map((item: any) => {
        return { user_id: item }
      })
      values.resources = processingData(values)
      if (type === '1') {
        setSaving1(true)
      }
      if (type === '2') {
        setSaving2(true)
      }
      if (currentType === 1) {
        const res = await service.post<ApiResponse<unknown>>(
          `/meetings`,
          values
        )
        if (res.code !== 200001)
          return message.error(`保存失败：${res.message}`)
        fetchMeetingCount()
        navigate(-1)
      }
      if (currentType === 2) {
        const res = await service.post<ApiResponse<unknown>>(
          `/meetings/${routeParams.id}`,
          values
        )
        if (res.code !== 200001)
          return message.error(`保存失败：${res.message}`)
        navigate(-1)
        fetchMeetingCount()
      }
    } finally {
      setSaving1(false)
      setSaving2(false)
    }
  }

  const handleReleaseMeeting = async () => {
    const meetingId = location.pathname.split('/release/')[1]
    setReleasePublishing(true)
    try {
      await handleSave()
      const res = await service.post<ApiResponse<unknown>>(
        `/meetings/${meetingId}/publish`,
        {}
      )
      if (res.code !== 200001) {
        message.error(`发布会议失败：${res.message}`)
      } else {
        message.success('发布会议成功')
        navigate('/group/meeting')
      }
      fetchMeetingCount()
    } finally {
      setReleasePublishing(false)
    }
  }

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const onFileDownload = async (fileId: string, name: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    const fileName = decodeURIComponent((name || '').split('/').pop() || 'file')
    try {
      const res = await service.get<Blob>(`/files/${fileId}/download`, {
        responseType: 'blob',
      })
      const blobUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
      message.destroy()
      message.success('文件下载成功')
    } catch (error: any) {
      message.destroy()
      message.open({
        type: 'error',
        content: error?.message,
        duration: 2,
      })
    }
  }

  const handleSaveMeeting = async () => {
    setSaving3(true)
    try {
      const values = await meetingInfoForm.validateFields()
      values.start_at = timeFormat(values.start_at)
      values.end_at = timeFormat(values.end_at)
      values.is_draft = false
      if (values.year) {
        values.year = dayjs(values.year).get('year')
      }
      values.implement = {
        officer: recursion(values.officer_value, 'name'),
        officer_id: recursion(values.officer_value, 'id'),
        leader: recursion(values.leader_value, 'name'),
        leader_id: recursion(values.leader_value, 'id'),
        dept_leader: recursion(values.dept_leader_value, 'name'),
        dept_leader_id: recursion(values.dept_leader_value, 'id'),
        dept_auditor: recursion(values.dept_auditor_value, 'name'),
        dept_auditor_id: recursion(values.dept_auditor_value, 'id'),
        cooperate_dept_leader: recursion(
          values.cooperate_dept_leader_value,
          'name'
        ),
        cooperate_dept_leader_id: recursion(
          values.cooperate_dept_leader_value,
          'id'
        ),
      }
      values.members = (values.membersData || []).map((item: any) => {
        return { user_id: item }
      })
      values.observers = (values.observers || []).map((item: any) => {
        return { user_id: item }
      })
      values.staff = (values.staff || []).map((item: any) => {
        return { user_id: item }
      })
      values.resources = processingData(values)
      // 补学材料
      if (values.bxclInfoList && values.bxclInfoList.length) {
        values.supplements = values.bxclInfoList.map((item: any) => {
          return {
            user_id: item.user_id,
            name: item.files?.[0]?.name,
            link: item.files?.[0]?.url,
            file_id: item.files?.[0]?.id,
          }
        })
      }
      // 会议补录
      values.after = {
        meeting_id: routeParams?.id,
        brief_name: values?.hyjbInfo?.[0]?.name,
        brief_link: values?.hyjbInfo?.[0]?.url,
        brief_file_id: values?.hyjbInfo?.[0]?.id,
        summary_name: values?.hyjyInfo?.[0]?.name,
        summary_link: values?.hyjyInfo?.[0]?.url,
        summary_file_id: values?.hyjyInfo?.[0]?.id,
      }
      const res = await service.post<ApiResponse<unknown>>(
        `/meetings/${routeParams?.id}`,
        values
      )
      if (res.code === 200001) {
        navigate(-1)
        return message.success('保存成功')
      }
      message.error('保存失败：' + res.message)
      fetchMeetingCount()
    } finally {
      setSaving3(false)
    }
  }

  const handleSave = async () => {
    const values = await meetingInfoForm.validateFields()
    if (values.start_at) {
      values.start_at = timeFormat(values.start_at)
    }
    if (values.end_at) {
      values.end_at = timeFormat(values.end_at)
    }
    if (values.year) {
      values.year = dayjs(values.year).get('year')
    }
    values.implement = {
      officer: recursion(values.officer_value, 'name'),
      officer_id: recursion(values.officer_value, 'id'),
      leader: recursion(values.leader_value, 'name'),
      leader_id: recursion(values.leader_value, 'id'),
      dept_leader: recursion(values.dept_leader_value, 'name'),
      dept_leader_id: recursion(values.dept_leader_value, 'id'),
      dept_auditor: recursion(values.dept_auditor_value, 'name'),
      dept_auditor_id: recursion(values.dept_auditor_value, 'id'),
      cooperate_dept_leader: recursion(
        values.cooperate_dept_leader_value,
        'name'
      ),
      cooperate_dept_leader_id: recursion(
        values.cooperate_dept_leader_value,
        'id'
      ),
    }
    values.members = (values.membersData || []).map((item: any) => {
      return { user_id: item }
    })
    values.observers = (values.observers || []).map((item: any) => {
      return { user_id: item }
    })
    values.staff = (values.staff || []).map((item: any) => {
      return { user_id: item }
    })
    values.resources = processingData(values)
    const res = await service.post<ApiResponse<unknown>>(
      `/meetings/${routeParams?.id}`,
      values
    )
    if (res.code !== 200001) {
      message.error('保存失败')
      throw new Error(res.message)
    }
  }

  const handleStartAttendMeetingAction = () => {
    Modal.confirm({
      title: `确定发起参会确认吗？`,
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        loading: attendMeetingLoading,
      },
      async onOk() {
        setAttendMeetingLoading(true)
        try {
          await handleSave()
          const res = await startAttendMeetingAction(meetingDetail.id)
          if (res.code !== 200001) {
            message.error(`发起参会确认失败：${res.message}`)
          } else {
            message.success('发起参会确认成功')
            navigate('/group/meeting')
          }
        } finally {
          setAttendMeetingLoading(false)
        }
      },
    })
  }

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Spin spinning />
      </div>
    )
  }

  const exchangeStudyListCanEdit =
    Number(currentType) !== 3 ||
    Number(currentType) !== 4 ||
    (searchParams.get('type') === 'hhsc' &&
      (userInfo?.roles?.includes('qt_undertaker') ||
        userInfo?.roles?.includes('sa') ||
        userOptionsV2.find((u) => u.id === userInfo?.id)))
  const showAttendMeeting =
    meetingDetail?.status === 'approved' &&
    (userInfo?.roles?.includes('qt_undertaker') ||
      userInfo?.roles?.includes('sa') ||
      userInfo?.permission_codes?.includes('LearnManage'))
  // const isApprovalLeader = [
  //   meetingDetail?.implement?.officer_value,
  //   meetingDetail?.implement?.leader_value,
  //   meetingDetail?.implement?.dept_leader_value,
  //   meetingDetail?.implement?.dept_auditor_value,
  // ]
  //   .filter((item) => !!item)
  //   .includes(userInfo?.id)
  const staffDisabled =
    (meetingDetail?.staff || [])
      .map((item: any) => item.user_id)
      .includes(userInfo?.id) && !userInfo?.roles?.includes('qt_undertaker')
  const undertakerDisabledBaseInfoAndLeader =
    meetingDetail?.status === 'confirming' &&
    userInfo?.roles?.includes('qt_undertaker')
  const disabledBaseInfo =
    Number(currentType) === 3 ||
    Number(currentType) === 4 ||
    showAttendMeeting ||
    staffDisabled ||
    undertakerDisabledBaseInfoAndLeader
  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          {
            title: '集中学习',
            href: '#',
            onClick: () => navigate(`/group/meeting`),
          },
          { title: currentTitle },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <p className="mb-4 font-semibold">会议信息</p>
        <Form
          form={meetingInfoForm}
          name="meetingInfoForm"
          initialValues={{ selfLearningTheme: '' }}
          clearOnDestroy
          onFinish={onCreatePlanFinish}
          onFinishFailed={onCreatePlanFinishFailed}
          autoComplete="off"
          labelCol={{ span: 4, xl: 2 }}
          labelAlign="left"
          className="hide-scrollbar w-full flex-1 overflow-auto overflow-x-hidden"
          disabled={currentType === 3 || currentType === 4}
        >
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="会议名称" className="mb-0 pb-0">
                <div className="flex gap-2">
                  <div className="w-[140px] shrink-0 grow-0">
                    <Form.Item
                      className="w-full"
                      name="year"
                      rules={[{ required: true, message: '请选择会议年度' }]}
                    >
                      <DatePicker
                        picker="year"
                        className="w-full"
                        disabled={disabledBaseInfo}
                      />
                    </Form.Item>
                  </div>
                  <div className="mt-[6px] shrink-0 grow-0">第</div>
                  <div className="w-[100px] shrink-0 grow-0">
                    <Form.Item
                      className="w-full"
                      name="count"
                      rules={[{ required: true, message: '输入会议次数' }]}
                    >
                      <InputNumber
                        className="w-full"
                        min={1}
                        placeholder="会议次数"
                        precision={0}
                        disabled={disabledBaseInfo}
                      />
                    </Form.Item>
                  </div>
                  <div className="mt-[6px] shrink-0 grow-0">次</div>
                  <div className="w-0 flex-1">
                    <Form.Item
                      className="w-full"
                      name="name"
                      rules={[{ required: true, message: '请填写会议名称' }]}
                    >
                      <Input
                        className="w-full"
                        allowClear
                        placeholder="请填写会议名称"
                        disabled={disabledBaseInfo}
                      />
                    </Form.Item>
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="是否计划内学习"
                name="is_plan"
                rules={[{ required: true, message: '请选择是否计划内学习' }]}
              >
                <Select
                  placeholder="请选择是否计划内学习"
                  options={[
                    { label: '是', value: '是' },
                    { label: '否', value: '否' },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="会议开始时间"
                labelCol={{ span: 8, xl: 4 }}
                name="start_at"
                rules={[{ required: true, message: '请选择会议开始时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="会议结束时间"
                labelCol={{ span: 8, xl: 4 }}
                name="end_at"
                rules={[{ required: true, message: '请选择会议结束时间' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  width={'100%'}
                  showTime
                  allowClear
                  placeholder="请选择年/月/日/时/分"
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="会议地点"
                name="room"
                rules={[{ required: true, message: '请填写会议地点' }]}
              >
                <Input
                  allowClear
                  placeholder="请填写会议地点"
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="参会人员"
                name="membersData"
                rules={[{ required: true, message: '请选择参会人员' }]}
              >
                <Select
                  mode="multiple"
                  options={
                    disabledBaseInfo
                      ? selectedUserInfos
                      : optionsFromAPI.filter(
                          (item) =>
                            item.roles.includes('first_resp') ||
                            item.roles.includes('zxz')
                        )
                  }
                  allowClear
                  placeholder="请选择参会人员"
                  onChange={(value: any) => {
                    setSelectedMembers(
                      (optionsFromAPI || [])
                        .filter((item: any) => value.includes(item.value))
                        .map((item: any) => ({
                          name: item.label,
                          id: item.value,
                        }))
                    )
                  }}
                  showSearch
                  filterOption={(input: any, option: any) =>
                    option?.label.toLowerCase().includes(input.toLowerCase())
                  }
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="列席人员" name="observers">
                <Select
                  mode="multiple"
                  options={
                    disabledBaseInfo ? selectedUserInfos : observersOption
                  }
                  allowClear
                  placeholder="请选择列席人员"
                  onChange={(value: any) => console.log(value)}
                  showSearch
                  filterOption={(input: any, option: any) =>
                    option?.label.toLowerCase().includes(input.toLowerCase())
                  }
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="工作人员"
                name="staff"
                rules={[{ required: true, message: '请选择工作人员' }]}
              >
                <Select
                  mode="multiple"
                  options={
                    disabledBaseInfo
                      ? selectedUserInfos
                      : userOptionsV2
                          .filter((item) => {
                            return (
                              (item.roles || []).includes('qt_undertaker') ||
                              (item.roles || []).includes('qt_head') ||
                              (item.roles || []).includes('xt_head') ||
                              (item.roles || []).includes('first_resp') ||
                              (item.roles || []).includes('zxz') ||
                              (item.roles || []).includes('qt_auditor') ||
                              (item.roles || []).includes('secretary')
                            )
                          })
                          .map((item) => ({
                            value: item.id,
                            label: item.full_name,
                          }))
                  }
                  allowClear
                  placeholder="请选择工作人员"
                  showSearch
                  filterOption={(input: any, option: any) =>
                    option?.label.toLowerCase().includes(input.toLowerCase())
                  }
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          {/* <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="列席人员"
                name="name"
                rules={[{ required: true, message: '请选择列席人员' }]}
              >
                <Select
                  options={[{ value: 'sample', label: 'sample' }]}
                  allowClear
                  placeholder="请选择列席人员"
                />
              </Form.Item>
            </Col>
          </Row> */}
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item
                label="学习主题"
                name="topic"
                rules={[{ required: true, message: '请填写学习主题' }]}
              >
                <Input
                  allowClear
                  placeholder="请填写学习主题"
                  disabled={disabledBaseInfo}
                />
              </Form.Item>
            </Col>
          </Row>
          <hr className="border-t border-dashed border-neutral-200" />
          <p className="my-4">会前自学</p>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item>
                <Radio.Group
                  value={zxType}
                  onChange={(val) => setZXType(val.target.value)}
                  options={[
                    {
                      value: 'attach',
                      label: '关联自学计划',
                    },
                    {
                      value: 'manual',
                      label: '手动添加',
                    },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
          {zxType === 'attach' && (
            <Row gutter={20}>
              <Col span={24}>
                <Form.Item label="关联自学计划" name="attachedLearnId">
                  <Select
                    allowClear
                    options={(learnOptions || []).map((item) => ({
                      value: item.id,
                      label: item.name,
                    }))}
                    filterOption={(input, option) => {
                      return (option?.label ?? '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                    }}
                  />
                </Form.Item>
              </Col>
              {attachedLearnId && (
                <Col span={24}>
                  <Spin spinning={attachedLearnDetailLoading}>
                    {attachedLearnResources.map((item, index) => (
                      <div key={item.id} className="mb-4">
                        <Row gutter={20}>
                          <Col xl={2} span={4}>
                            <span className="text-gray-500">
                              会前自学{index + 1}:
                            </span>
                          </Col>
                          <Col xl={22} span={20}>
                            <div>{item.content}</div>
                          </Col>
                        </Row>
                        <Row gutter={20}>
                          <Col xl={2} span={4}>
                            <span className="text-gray-500">
                              相关材料{index + 1}:
                            </span>
                          </Col>
                          <Col xl={22} span={20}>
                            {item.mode === '电子链接' && (
                              <div className="flex flex-1 gap-2">
                                <a href={item.link} target="_blank">
                                  查看
                                </a>
                              </div>
                            )}
                            {item.mode === '电子文档' && (
                              <div className="flex flex-1 gap-2">
                                <a onClick={() => onFilePreview(item.file_id)}>
                                  查看
                                </a>
                                <a
                                  onClick={() =>
                                    onFileDownload(item.file_id, item.name)
                                  }
                                >
                                  下载
                                </a>
                              </div>
                            )}
                            {item.mode === '纸质文档' && '--'}
                          </Col>
                        </Row>
                      </div>
                    ))}
                  </Spin>
                </Col>
              )}
            </Row>
          )}
          {zxType === 'manual' && (
            <div>
              <Form.List name="selfStudyList">
                {(fields: any, { add, remove }, { errors }) => (
                  <>
                    {fields.map((field: any, index: any) => (
                      <Form.Item required={false} key={field.key}>
                        <div className="flex gap-4">
                          <div className="flex-1">
                            <Form.Item
                              {...field}
                              validateTrigger={['onChange', 'onBlur']}
                              noStyle
                              key={field.key}
                            >
                              <Row gutter={20}>
                                <Col span={24}>
                                  <Form.Item
                                    name={[field.name, 'content']}
                                    label={`会前自学${index + 1}`}
                                    labelCol={{ span: 4, xl: 2 }}
                                    labelAlign="left"
                                    rules={[
                                      {
                                        required: true,
                                        message: `会前自学${index + 1}`,
                                      },
                                    ]}
                                  >
                                    <Input
                                      allowClear
                                      placeholder="请填写会前自学内容"
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                              <Row gutter={20}>
                                <Col span={24}>
                                  <Form.Item
                                    label="相关材料"
                                    name={[field.name, 'files']}
                                    labelCol={{ span: 4, xl: 2 }}
                                    labelAlign="left"
                                  >
                                    <FileUpload
                                      maxCount={1}
                                      disabled={currentType === 4}
                                    ></FileUpload>
                                  </Form.Item>
                                </Col>
                              </Row>
                            </Form.Item>
                          </div>
                          <div className="flex-shrink-0 flex-grow-0">
                            <MinusCircleOutlined
                              onClick={() => remove(field.name)}
                            />
                          </div>
                        </div>
                      </Form.Item>
                    ))}
                    <Form.Item>
                      <Button
                        color="primary"
                        variant="dashed"
                        size="large"
                        style={{ width: '100%' }}
                        className="secondary-btn-dashed"
                        type="dashed"
                        onClick={() => add()}
                      >
                        <PlusOutlined />
                        添加一组会前自学内容
                      </Button>

                      <Form.ErrorList errors={errors} />
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </div>
          )}

          <hr className="mt-6 border-t border-dashed border-neutral-200" />
          <p className="my-4">传达学习</p>

          <Form.List name="conveyStudyList">
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map((field, index) => (
                  <Form.Item required={false} key={field.key}>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <Form.Item
                          {...field}
                          validateTrigger={['onChange', 'onBlur']}
                          noStyle
                          key={field.key}
                        >
                          <Row gutter={20}>
                            <Col span={24}>
                              <Form.Item
                                name={[field.name, 'content']}
                                label={`传达学习${index + 1}`}
                                labelCol={{ span: 4, xl: 2 }}
                                labelAlign="left"
                              >
                                <Input
                                  allowClear
                                  placeholder="请填写传达学习内容"
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row gutter={20}>
                            <Col span={24}>
                              <Form.Item
                                label="相关材料"
                                name={[field.name, 'files']}
                                labelCol={{ span: 4, xl: 2 }}
                                labelAlign="left"
                              >
                                <FileUpload
                                  maxCount={1}
                                  disabled={currentType === 4}
                                ></FileUpload>
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </div>
                      <div className="flex-shrink-0 flex-grow-0">
                        <Button
                          type="link"
                          icon={<MinusCircleOutlined />}
                          danger
                          onClick={() => remove(field.name)}
                        />
                      </div>
                    </div>
                  </Form.Item>
                ))}
                <Form.Item>
                  <Button
                    color="primary"
                    variant="dashed"
                    size="large"
                    style={{ width: '100%' }}
                    className="secondary-btn-dashed"
                    type="dashed"
                    onClick={() => add()}
                  >
                    <PlusOutlined />
                    添加一组传达学习内容
                  </Button>

                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>

          <hr className="mt-6 border-t border-dashed border-neutral-200" />
          <p className="my-4">交流研讨</p>
          <Form.List name="exchangeStudyList">
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map((field, index) => (
                  <Form.Item required={false} key={field.key}>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <Form.Item
                          {...field}
                          validateTrigger={['onChange', 'onBlur']}
                          noStyle
                          key={field.key}
                        >
                          <Row gutter={20}>
                            <Col span={24}>
                              <Form.Item
                                label={`主题${index + 1}`}
                                name={[field.name, 'content']}
                                labelCol={{ span: 4, xl: 2 }}
                                labelAlign="left"
                              >
                                <Input
                                  allowClear
                                  placeholder="请填写交流研讨主题"
                                  disabled={!exchangeStudyListCanEdit}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row gutter={20}>
                            <Col span={24}>
                              <Form.Item
                                label="发言人"
                                name={[field.name, 'speak']}
                                labelCol={{ span: 4, xl: 2 }}
                                labelAlign="left"
                              >
                                <Select
                                  allowClear
                                  placeholder="请填写发言人"
                                  labelInValue={true}
                                  options={selectedMembers.map((item) => ({
                                    value: item.id,
                                    label: item.name,
                                  }))}
                                  disabled={!exchangeStudyListCanEdit}
                                />
                              </Form.Item>
                            </Col>
                          </Row>
                          <Row gutter={20}>
                            <Col span={24}>
                              <Form.Item
                                label="相关材料"
                                name={[field.name, 'files']}
                                labelCol={{ span: 4, xl: 2 }}
                                labelAlign="left"
                              >
                                <FileUpload
                                  maxCount={1}
                                  disabled={!exchangeStudyListCanEdit}
                                ></FileUpload>
                              </Form.Item>
                            </Col>
                          </Row>
                        </Form.Item>
                      </div>
                      <div className="flex-shrink-0 flex-grow-0">
                        <Button
                          type="link"
                          icon={<MinusCircleOutlined />}
                          danger
                          onClick={() => remove(field.name)}
                        />
                      </div>
                    </div>
                  </Form.Item>
                ))}
                <Form.Item>
                  <Button
                    color="primary"
                    variant="dashed"
                    size="large"
                    style={{ width: '100%' }}
                    className="secondary-btn-dashed"
                    type="dashed"
                    disabled={!exchangeStudyListCanEdit}
                    onClick={() => add()}
                  >
                    <PlusOutlined />
                    添加一组交流研讨内容
                  </Button>

                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>

          <hr className="mt-6 border-t border-dashed border-neutral-200" />
          <p className="my-4">其他材料</p>
          <Row gutter={20}>
            <Col span={24}>
              <Form.Item label="材料名称" name="other">
                <FileUpload maxCount={10}></FileUpload>
              </Form.Item>
            </Col>
          </Row>
          {/* <p className="mb-4 font-semibold">审核领导</p> */}
          {/* <p className="my-4"></p> */}
          {/* <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="党委书记"
                labelCol={{ span: 8, xl: 4 }}
                name="officer_value"
                rules={[{ required: true, message: '请选择党委书记' }]}
              >
                <Select
                  options={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                      ? selectedUserInfos
                      : optionsFromAPI.filter((item) =>
                          item.roles.includes('first_resp')
                        )
                  }
                  onChange={(value) => console.log(value)}
                  allowClear
                  disabled={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                  }
                  placeholder="请选择党委书记"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="分管领导"
                labelCol={{ span: 8, xl: 4 }}
                name="leader_value"
                rules={[{ required: true, message: '请选择分管领导' }]}
              >
                <Select
                  options={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                      ? selectedUserInfos
                      : optionsFromAPI.filter((item) =>
                          item.roles.includes('zxz')
                        )
                  }
                  allowClear
                  disabled={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                  }
                  placeholder="请选择分管领导"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="牵头部门负责人"
                labelCol={{ span: 8, xl: 4 }}
                name="dept_leader_value"
                rules={[{ required: true, message: '请选择牵头部门负责人' }]}
              >
                <Select
                  options={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                      ? selectedUserInfos
                      : optionsFromAPI.filter((item) =>
                          item.roles.includes('qt_head')
                        )
                  }
                  allowClear
                  disabled={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                  }
                  placeholder="请选择牵头部门负责人"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="牵头部门审核人"
                labelCol={{ span: 8, xl: 4 }}
                name="dept_auditor_value"
                rules={[{ required: true, message: '请选择牵头部门审核人' }]}
              >
                <Select
                  options={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                      ? selectedUserInfos
                      : optionsFromAPI.filter((item) =>
                          item.roles.includes('qt_auditor')
                        )
                  }
                  allowClear
                  disabled={
                    currentType === 3 ||
                    currentType === 4 ||
                    staffDisabled ||
                    undertakerDisabledBaseInfoAndLeader
                  }
                  placeholder="请选择牵头部门审核人"
                />
              </Form.Item>
            </Col>
          </Row> */}
          {/* <Row gutter={20}>
            <Col span={12}>
              <Form.Item
                label="协同部门负责人"
                labelCol={{ span: 6 }}
                name="cooperate_dept_leader_value"
              >
                <Select
                  options={optionsFromAPI.filter((item) =>
                    item.roles.includes('xt_head')
                  )}
                  allowClear
                  placeholder="请选择协同部门负责人"
                />
              </Form.Item>
            </Col>
          </Row> */}

          {currentType === 4 && (
            <>
              <hr className="mb-4 border-t border-gray-200" />
              <p className="mb-4 font-semibold">会后补录</p>
              <Row gutter={20}>
                <Col span={24}>
                  <Form.Item label="会议简报" name="hyjbInfo">
                    <FileUpload
                      disabled={
                        currentType === 4 &&
                        searchParams.get('type') === 'bcxtcl'
                      }
                    ></FileUpload>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={20}>
                <Col span={24}>
                  <Form.Item label="会议纪要" name="hyjyInfo">
                    <FileUpload
                      disabled={
                        currentType === 4 &&
                        searchParams.get('type') === 'bcxtcl'
                      }
                    ></FileUpload>
                  </Form.Item>
                </Col>
              </Row>
              <p className="my-4">补学材料</p>
              <div>
                <Form.List name="bxclInfoList">
                  {(fields, { add, remove }, { errors }) => (
                    <>
                      {fields.map((field, index) => (
                        <Form.Item key={field.key}>
                          <div className="flex gap-4">
                            <div className="flex-1">
                              <Form.Item
                                {...field}
                                validateTrigger={['onChange', 'onBlur']}
                                noStyle
                                key={field.key}
                              >
                                <Row gutter={20}>
                                  <Col span={24}>
                                    <Form.Item
                                      label={`请假人${index + 1}`}
                                      name={[field.name, 'user_id']}
                                      labelCol={{ span: 4, xl: 2 }}
                                      rules={[
                                        {
                                          required: true,
                                          message: `请选择请假人${index + 1}`,
                                        },
                                      ]}
                                    >
                                      <Select
                                        options={
                                          currentType === 4 &&
                                          searchParams.get('type') === 'bcxtcl'
                                            ? selectedUserInfos
                                            : optionsFromAPI.filter(
                                                (item) =>
                                                  item.roles.includes(
                                                    'first_resp'
                                                  ) ||
                                                  item.roles.includes('zxz')
                                              )
                                        }
                                        allowClear
                                        placeholder="请选择请假人"
                                        disabled={
                                          currentType === 4 &&
                                          searchParams.get('type') === 'bcxtcl'
                                        }
                                      />
                                    </Form.Item>
                                  </Col>
                                </Row>
                                <Row gutter={20}>
                                  <Col span={24}>
                                    <Form.Item
                                      label="相关材料"
                                      name={[field.name, 'files']}
                                      labelCol={{ span: 4, xl: 2 }}
                                    >
                                      <FileUpload
                                        disabled={
                                          currentType === 4 &&
                                          searchParams.get('type') === 'bcxtcl'
                                        }
                                      ></FileUpload>
                                    </Form.Item>
                                  </Col>
                                </Row>
                              </Form.Item>
                            </div>
                            <div className="flex-shrink-0 flex-grow-0">
                              <Button
                                disabled={
                                  currentType === 4 &&
                                  searchParams.get('type') === 'bcxtcl'
                                }
                                type="link"
                                icon={<MinusCircleOutlined />}
                                danger
                                onClick={() => remove(field.name)}
                              />
                            </div>
                          </div>
                        </Form.Item>
                      ))}
                      <Form.Item>
                        <Button
                          disabled={
                            currentType === 4 &&
                            searchParams.get('type') === 'bcxtcl'
                          }
                          color="primary"
                          variant="dashed"
                          size="large"
                          style={{ width: '100%' }}
                          className="secondary-btn-dashed"
                          type="dashed"
                          onClick={() => add()}
                        >
                          <PlusOutlined />
                          添加一组补学材料
                        </Button>

                        <Form.ErrorList errors={errors} />
                      </Form.Item>
                    </>
                  )}
                </Form.List>
              </div>
            </>
          )}
        </Form>

        <Flex className="my-4" gap="small" wrap justify="end">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() => navigate(`/group/meeting`)}
          >
            取消
          </Button>
          {(currentType === 1 || currentType === 2) && (
            <>
              <Button
                color="primary"
                variant="solid"
                size="large"
                className="secondary-btn"
                onClick={() => handleSaveAndApply('2')}
                loading={saving2}
              >
                {showAttendMeeting ||
                staffDisabled ||
                undertakerDisabledBaseInfoAndLeader
                  ? '保存数据'
                  : '保存为草稿'}
              </Button>
              {!showAttendMeeting &&
                !staffDisabled &&
                !undertakerDisabledBaseInfoAndLeader && (
                  <Button
                    color="primary"
                    variant="solid"
                    size="large"
                    className="danger-btn"
                    onClick={() => handleSaveAndApply('1')}
                    loading={saving1}
                  >
                    保存并发起参会确认
                  </Button>
                )}
            </>
          )}
          {currentType === 3 && (
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={handleReleaseMeeting}
              loading={releasePublishing}
            >
              发布会议
            </Button>
          )}
          {currentType === 4 && (
            <Button
              loading={saving3}
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={handleSaveMeeting}
            >
              保存数据
            </Button>
          )}
          {showAttendMeeting && (
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              loading={attendMeetingLoading}
              onClick={handleStartAttendMeetingAction}
            >
              发起参会确认
            </Button>
          )}
        </Flex>
      </div>
    </div>
  )
}

export default Meeting
