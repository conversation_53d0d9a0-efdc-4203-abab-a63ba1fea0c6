import { B<PERSON><PERSON>rumb, Divider, Flex, Spin, message } from 'antd'
import { useState } from 'react'
import { useMount } from 'react-use'
import fivePointedStar from '~/assets/images/five-pointed-star.png'
import { ApiResponse, service } from '~/lib/service'

interface DocItem {
  id: string
  title: string
  link: string
  timestamp: string
  category: string
  created_at: string
  updated_at: string
}

const PersonDoc: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [theoryDocs, setTheoryDocs] = useState<DocItem[]>([])
  const [meetingDocs, setMeetingDocs] = useState<DocItem[]>([])
  const [educationDocs, setEducationDocs] = useState<DocItem[]>([])

  const items = [
    {
      category: '思想理论',
      moreUrl:
        'https://www.xuexi.cn/xxqg.html?id=3cba33e067d64ded8a1a503f0774675c',
      data: theoryDocs,
    },
    {
      category: '重大会议',
      moreUrl:
        'https://www.xuexi.cn/xxqg.html?id=e92ce51ccdb1414abc3d42b927cc69bf',
      data: meetingDocs,
    },

    {
      category: '党内教育',
      moreUrl: 'https://www.12371.cn/djxx/djls/',
      data: educationDocs,
    },
    {
      category: '党章党规',
      moreUrl:
        'https://www.xuexi.cn/db30203b53baae523b23ca9583a33612/db93dd803f9534aa6609cc91fa3fba9b.html',
      data: [
        {
          title: '《中国共产党章程》第一章 党员',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=7271935631146878458&item_id=7271935631146878458',
          timestamp: '2023-03-23',
        },
        {
          title: '《中国共产党章程》第二章 党的组织制度',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=2330203352550072114&item_id=2330203352550072114',
          timestamp: '2023-03-23',
        },
        {
          title: '《中国共产党章程》第三章 党的中央组织',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=9784629208180926923&item_id=9784629208180926923',
          timestamp: '2023-03-23',
        },
        {
          title: '《中国共产党章程》第四章 党的地方组织',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=11665158251860880734&item_id=11665158251860880734',
          timestamp: '2023-03-23',
        },
        {
          title: '《中国共产党章程》第五章 党的基层组织',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=3222777675009084780&item_id=3222777675009084780',
          timestamp: '2023-03-23',
        },
        {
          title: '《中国共产党章程》第六章 党的干部',
          link: 'https://www.xuexi.cn/lgpage/detail/index.html?id=17434658743466799110&item_id=17434658743466799110',
          timestamp: '2023-03-23',
        },
      ],
    },
  ]

  const DOC_CATEGORIES = [
    { type: 'theory', setter: setTheoryDocs },
    { type: 'meeting', setter: setMeetingDocs },
    { type: 'education', setter: setEducationDocs },
  ] as const

  const getDocs = async () => {
    setLoading(true)

    try {
      const responses = await Promise.all(
        DOC_CATEGORIES.map(({ type }) =>
          service.get<ApiResponse<DocItem[]>>('/libraries/topn', {
            params: { category: type },
          })
        )
      )

      const isAllSuccess = responses.every((res) => res.code === 200001)

      if (isAllSuccess) {
        responses.forEach((res, index) => {
          DOC_CATEGORIES[index].setter(res.data)
        })
      } else {
        const errorMessage = responses.find(
          (res) => res.code !== 200001
        )?.message
        message.error(errorMessage ?? '获取数据失败')
      }
    } catch (error) {
      message.error('网络请求失败')
      console.error('获取文档失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useMount(getDocs)

  return (
    <Spin spinning={loading}>
      <div className="flex h-full flex-col">
        <Breadcrumb
          separator=">"
          items={[{ title: '个人学习' }, { title: '资料文库' }]}
        />

        {items.length > 0 ? (
          items.map((item) => (
            <div key={item.category}>
              <Divider
                variant="dashed"
                style={{
                  borderColor: '#ad080f',
                }}
              >
                <Flex align="center" justify="center">
                  <img src={fivePointedStar} alt="star" className="w-5" />
                  <span
                    className="mx-2"
                    style={{
                      background:
                        'linear-gradient(to bottom, #ce942d, #ad080f)',
                      WebkitBackgroundClip: 'text',
                      color: 'transparent',
                      fontSize: '29px',
                      fontWeight: 600,
                      margin: '0 8px',
                    }}
                  >
                    {item.category}
                  </span>
                  <img src={fivePointedStar} alt="star" className="w-5" />
                </Flex>
              </Divider>
              <div style={{ backgroundColor: 'white', padding: '20px 20px' }}>
                <ul>
                  {item.data.map((data) => (
                    <li
                      key={data.link}
                      style={{
                        listStyleType: 'disc',
                        color: 'red',
                        marginLeft: '20px',
                        marginTop: '20px',
                        marginBottom: '20px',
                      }}
                    >
                      <div className="flex items-center gap-2" key={data.link}>
                        <a
                          href={data.link}
                          target="_blank"
                          className="flex-1 truncate text-base text-black"
                        >
                          {data.title}
                        </a>
                        <span className="flex-shrink-0 flex-grow-0 text-[#999]">
                          {data.timestamp.substring(0, 10)}
                        </span>
                      </div>
                    </li>
                  ))}
                </ul>
                <ul style={{ textAlign: 'right' }}>
                  <a href={item.moreUrl} target="_blank">
                    更多&gt;&gt;&gt;
                  </a>
                </ul>
              </div>
            </div>
          ))
        ) : (
          <p>没有可显示的内容</p>
        )}
      </div>
    </Spin>
  )
}

export default PersonDoc
