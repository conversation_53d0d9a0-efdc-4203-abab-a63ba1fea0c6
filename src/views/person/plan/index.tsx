import { ExclamationCircleFilled } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Tabs,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useAsync } from 'react-use'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import {
  create<PERSON>earn,
  delLearn,
  queryLearnList,
  updateLearn,
} from '~/apis/person/plan'
import { Learn } from '~/models/person/plan'
import { PageParams } from '~/models/common/pageParams'
import Permission from '~/components/permission'
import { useAuthStore } from '~/store/auth'

const PersonPlan: React.FC = () => {
  const { userInfo } = useAuthStore()
  const [pageParams, setPageParams] = useState<
    PageParams & { company_id?: string }
  >({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<Learn[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [selectRowKeys, setSelectRowKeys] = useState<string[]>([])
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [modalTitle, setModalTitle] = useState('创建学习计划文件夹')
  const [createPlanForm] = Form.useForm()
  const [isEditing, setIsEditing] = useState(false)
  const [currentRecord, setCurrentRecord] = useState<Learn | null>(null)
  const [tab, setTab] = useState('本级党组织学习计划')
  const [tableLoading, setTableLoading] = useState(false)

  const { confirm } = Modal
  const navigate = useNavigate()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '自学主题',
      dataIndex: 'name',
      key: 'name',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '党组织名称',
      dataIndex: 'company_name',
      key: 'company_name',
      minWidth: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 220,
      align: 'center',
      render: (_, record: Learn) => (
        <Flex wrap justify="center">
          {tab === '本级党组织学习计划' && (
            <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() =>
                  setCreateModalVisibleVoid(true, '修改学习计划', record)
                }
              >
                修改
              </Button>
            </Permission>
          )}
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() =>
              navigate(
                `${record.id}/list?learn_name=${record.name}&type=${tab === '其他党组织学习计划' ? 'other' : 'self'}`
              )
            }
          >
            查看详情
          </Button>
          {tab === '本级党组织学习计划' && (
            <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
              <Button
                type="link"
                size="large"
                className="danger-btn-link"
                onClick={() => handleRowDelete(record)}
              >
                删除
              </Button>
            </Permission>
          )}
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  const { loading } = useAsync(async () => {
    await getTableData({ ...pageParams, company_id: userInfo?.company_id })
  })

  const getTableData = async (params: PageParams = pageParams) => {
    setTableLoading(true)
    try {
      const res = await queryLearnList(params)
      if (res.code !== 200001) {
        setTableData([])
        setTableTotal(0)
      } else {
        setTableData(res.data.list)
        setTableTotal(res.data.pager.total)
      }
    } finally {
      setTableLoading(false)
    }
  }

  const handleBatchDelete = () => {
    if (selectRowKeys.length === 0) {
      message.warning('请选择要删除的数据')
      return
    }
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除选中数据?',
      centered: true,
      okButtonProps: { rootClassName: 'primary-btn', className: 'primary-btn' },
      onOk() {
        handleDel(selectRowKeys)
      },
    })
  }

  const handleRowDelete = (row: Learn) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      onOk() {
        handleDel([row.id])
      },
    })
  }

  const handleDel = (ids: string[]) => {
    delLearn(ids).then((res) => {
      if (res.code !== 200001) {
        message.error(`删除失败： ${res.message}`)
      } else {
        message.success('删除成功')
        const _p = {
          ...pageParams,
        }
        if (tab === '本级党组织学习计划') {
          _p.company_id = userInfo?.company_id
        }
        getTableData(_p)
      }
    })
  }

  const setCreateModalVisibleVoid = (
    state: boolean,
    title: string,
    record?: Learn
  ) => {
    setModalTitle(title)
    setCreateModalVisible(state)
    if (record) {
      createPlanForm.setFieldsValue({ name: record.name })
      setCurrentRecord(record)
      setIsEditing(true)
    } else {
      createPlanForm.resetFields()
      setCurrentRecord(null)
      setIsEditing(false)
    }
  }

  const onCreatePlanFinish = (values: Learn) => {
    if (isEditing && currentRecord) {
      updateLearn(currentRecord.id, values.name).then((res) => {
        if (res.code !== 200001) {
          message.error(`保存失败： ${res.message}`)
        } else {
          setCreateModalVisible(false)
          const _p = {
            ...pageParams,
          }
          if (tab === '本级党组织学习计划') {
            _p.company_id = userInfo?.company_id
          }
          getTableData(_p)
        }
      })
    } else {
      createLearn(values.name).then((res) => {
        if (res.code !== 200001) {
          message.error(`保存失败： ${res.message}`)
        } else {
          setCreateModalVisible(false)
          const _p = {
            ...pageParams,
          }
          if (tab === '本级党组织学习计划') {
            _p.company_id = userInfo?.company_id
          }
          getTableData(_p)
        }
      })
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '个人学习' }, { title: '自学计划' }]}
      />
      <div className="mt-4">
        <Tabs
          type="card"
          tabBarGutter={0}
          defaultActiveKey="本级党组织学习计划"
          onChange={(key) => {
            setTab(key)
            const _p = {
              ...pageParams,
            }
            if (key === '本级党组织学习计划') {
              _p.company_id = userInfo?.company_id
            }
            getTableData(_p)
          }}
          items={[
            {
              key: '本级党组织学习计划',
              label: '本级党组织学习计划',
              children: (
                <div className="bg-white p-4">
                  <Flex className="mb-4" gap="small" wrap>
                    <Permission
                      roles={['qt_undertaker']}
                      codes={['LearnManage']}
                    >
                      <Button
                        color="primary"
                        variant="solid"
                        size="large"
                        className="primary-btn"
                        onClick={() =>
                          setCreateModalVisibleVoid(true, '创建学习计划')
                        }
                      >
                        创建学习计划
                      </Button>
                      <Button
                        color="danger"
                        variant="filled"
                        size="large"
                        className="danger-btn-outlined"
                        onClick={handleBatchDelete}
                      >
                        批量删除
                      </Button>
                    </Permission>
                  </Flex>
                  <CustomTable
                    className="h-full"
                    loading={loading || tableLoading}
                    columns={columns}
                    dataSource={tableData}
                    rowSelection={{
                      type: 'checkbox',
                      fixed: true,
                      onChange: (selectedRowKeys) => {
                        setSelectRowKeys(selectedRowKeys as string[])
                      },
                    }}
                    total={tableTotal}
                    onChange={(page, pageSize) => {
                      const params = {
                        ...pageParams,
                        current: page,
                        pageSize: pageSize,
                      }
                      setPageParams(params)
                      const _p = {
                        ...params,
                      }
                      if (tab === '本级党组织学习计划') {
                        _p.company_id = userInfo?.company_id
                      }
                      getTableData(_p)
                    }}
                  ></CustomTable>
                </div>
              ),
            },
            {
              key: '其他党组织学习计划',
              label: '其他党组织学习计划',
              children: (
                <div className="bg-white p-4">
                  {/* <Flex className="mb-4" gap="small" wrap>
                    <Permission
                      roles={['qt_undertaker']}
                      codes={['LearnManage']}
                    >
                      <Button
                        color="primary"
                        variant="solid"
                        size="large"
                        className="primary-btn"
                        onClick={() =>
                          setCreateModalVisibleVoid(true, '创建学习计划')
                        }
                      >
                        创建学习计划
                      </Button>
                      <Button
                        color="danger"
                        variant="filled"
                        size="large"
                        className="danger-btn-outlined"
                        onClick={handleBatchDelete}
                      >
                        批量删除
                      </Button>
                    </Permission>
                  </Flex> */}
                  <CustomTable
                    className="h-full"
                    loading={loading || tableLoading}
                    columns={columns}
                    dataSource={tableData}
                    rowSelection={{
                      type: 'checkbox',
                      fixed: true,
                      onChange: (selectedRowKeys) => {
                        setSelectRowKeys(selectedRowKeys as string[])
                      },
                    }}
                    total={tableTotal}
                    onChange={(page, pageSize) => {
                      const params = {
                        ...pageParams,
                        current: page,
                        pageSize: pageSize,
                      }
                      setPageParams(params)
                      getTableData(params)
                    }}
                  ></CustomTable>
                </div>
              ),
            },
          ]}
          style={{ color: '#000000' }}
        ></Tabs>

        {/* <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{
            type: 'checkbox',
            fixed: true,
            onChange: (selectedRowKeys) => {
              setSelectRowKeys(selectedRowKeys as string[])
            },
          }}
          total={tableTotal}
          onChange={(page, pageSize) => {
            const params = { ...pageParams, current: page, pageSize: pageSize }
            setPageParams(params)
            getTableData(params)
          }}
        ></CustomTable> */}
      </div>

      <Modal
        title={modalTitle}
        open={createModalVisible}
        centered
        destroyOnClose
        onOk={() => createPlanForm.submit()}
        onCancel={() => setCreateModalVisible(false)}
      >
        <Form
          form={createPlanForm}
          name="createPlanForm"
          initialValues={{ name: '' }}
          clearOnDestroy
          onFinish={onCreatePlanFinish}
          autoComplete="off"
          labelAlign="left"
        >
          <Form.Item
            label="自学主题"
            name="name"
            rules={[{ required: true, message: '请输入' }]}
          >
            <Input allowClear placeholder="请输入" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PersonPlan
