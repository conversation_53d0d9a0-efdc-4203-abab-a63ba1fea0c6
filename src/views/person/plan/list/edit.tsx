import {
  Button,
  Form,
  Input,
  message,
  Select,
  Flex,
  Breadcrumb,
  Space,
  Spin,
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useAsync } from 'react-use'
import FileUpload from '~/components/upload-file'
import {
  createLearnResource,
  queryLearnResource,
  updateLearnResource,
} from '~/apis/person/plan/list'
import { LearnResource } from '~/models/person/plan/list'
import Permission from '~/components/permission'

const PersonPlanListEdit: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { id, list_id } = useParams()
  const [saveLoading] = useState(false)
  const [form] = Form.useForm()

  const queryParams = new URLSearchParams(location.search)
  const learnName = queryParams.get('learn_name')

  const [selectedMode, setSelectedMode] = useState<string>()

  const { loading } = useAsync(async () => {
    if (list_id) {
      await getLearnResourceDetail()
    }
  })

  const getLearnResourceDetail = async () => {
    const res = await queryLearnResource(list_id!)
    if (res.code !== 200001) {
      message.error(res.message)
    } else {
      const data = {
        ...res.data,
        files: [{ url: res.data.name }],
      }
      form.setFieldsValue(data)
      setSelectedMode(res.data.mode)
    }
  }

  // 处理表单提交
  const handleSubmit = async (values: LearnResource) => {
    const { files, ...params } = values

    if (files && files.length > 0) {
      const file = files[0] as any
      params.name = file.url
      params.file_id = file.id
    }

    if (list_id) {
      const res = await updateLearnResource(list_id, params)
      if (res.code !== 200001) {
        message.error(`保存失败： ${res.message}`)
      } else {
        message.success('保存成功')
        navigateToList()
      }
    } else {
      const res = await createLearnResource(id!, params)
      if (res.code !== 200001) {
        message.success(`保存失败： ${res.message}`)
      } else {
        message.success('保存成功')
        navigateToList()
      }
    }
  }

  const navigateToList = () => {
    navigate(`/person/plan/${id}/list?learn_name=${learnName}`)
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '个人自学' },
          {
            title: '自学计划',
            href: '#',
            onClick: () => navigate(`/person/plan`),
          },
          {
            title: learnName,
            href: '#',
            onClick: navigateToList,
          },
          { title: '上传资料' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col items-center bg-white">
        <div className="w-full border-b border-gray-200 p-4 text-base font-medium">
          上传资料
          <Space size="small" className="ml-20 font-normal text-[#d32f2f]">
            <ExclamationCircleOutlined size={120} />
            提示：所有上传的资料，均公开给系统用户可见
          </Space>
        </div>
        <Spin
          spinning={loading}
          wrapperClassName="w-3/4 flex-col flex h-0 flex-1"
        >
          <Form
            form={form}
            labelCol={{ span: 4 }}
            labelAlign="left"
            className="hide-scrollbar mt-4 flex h-full flex-1 flex-col overflow-x-hidden p-4"
            onFinish={handleSubmit}
          >
            <Form.Item
              label="自学内容"
              name="content"
              rules={[{ required: true, message: '请输入自学内容' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="学习形式"
              name="mode"
              rules={[{ required: true, message: '请输入学习形式' }]}
            >
              <Select
                placeholder="请选择"
                onChange={(value) => setSelectedMode(value)}
              >
                <Select.Option value="电子链接">电子链接</Select.Option>
                <Select.Option value="电子文档">电子文档</Select.Option>
                <Select.Option value="纸质文档">纸质文档</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="files"
              label="上传资料"
              hidden={selectedMode !== '电子文档'}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (
                      getFieldValue('mode') === '电子文档' &&
                      (!value || value.length === 0)
                    ) {
                      return Promise.reject('请上传电子文档')
                    }
                    return Promise.resolve()
                  },
                }),
              ]}
            >
              <FileUpload></FileUpload>
            </Form.Item>
            <Form.Item
              label="电子链接"
              name="link"
              hidden={selectedMode !== '电子链接'}
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('mode') === '电子链接' && !value) {
                      return Promise.reject('请输入电子链接')
                    }
                    return Promise.resolve()
                  },
                }),
              ]}
            >
              <Input placeholder="请输入电子链接" />
            </Form.Item>
          </Form>
        </Spin>
        <Flex justify="end" gap="small" className="w-full p-4">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={navigateToList}
          >
            取消
          </Button>
          <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="danger-btn"
              loading={saveLoading}
              onClick={() => form.submit()}
            >
              保存
            </Button>
          </Permission>
        </Flex>
      </div>
    </div>
  )
}

export default PersonPlanListEdit
