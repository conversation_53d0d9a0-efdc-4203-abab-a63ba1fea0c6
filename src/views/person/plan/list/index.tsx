import { ExclamationCircleFilled } from '@ant-design/icons'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Flex, message, Modal } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useAsync } from 'react-use'
import { useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { LearnResource } from '~/models/person/plan/list'
import {
  delLearnResources,
  queryLearnResourceList,
} from '~/apis/person/plan/list'
import { PageParams } from '~/models/common/pageParams'
import Permission from '~/components/permission'
import { ApiResponse, service } from '~/lib/service'

const PersonPlanList: React.FC = () => {
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<LearnResource[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [selectRowKeys, setSelectRowKeys] = useState<string[]>([])
  const [previewDocModal, setPreviewDocModal] = useState(false)
  const [previewDocUrl, setPreviewDocUrl] = useState('')
  const location = useLocation()
  const { id } = useParams()

  const queryParams = new URLSearchParams(location.search)
  const learnName = queryParams.get('learn_name')
  const type = queryParams.get('type')

  const { confirm } = Modal
  const navigate = useNavigate()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '自学内容',
      dataIndex: 'content',
      key: 'content',
      width: 500,
      align: 'left',
    },
    {
      title: '学习形式',
      dataIndex: 'mode',
      key: 'mode',
      width: 120,
      align: 'left',
    },
    {
      title: '上传人',
      dataIndex: 'creator',
      key: 'creator ',
      width: 150,
      align: 'left',
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 240,
      align: 'center',
      fixed: 'right',
      render: (_, record: LearnResource) => (
        <Flex wrap justify="center">
          {record.mode === '电子链接' ? (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              href={record.link}
              target="_blank"
            >
              查看
            </Button>
          ) : (
            <>
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() =>
                  getPreviewDoc(record.file_id as string, record.name)
                }
              >
                查看
              </Button>
              <Modal
                open={previewDocModal}
                width="80%"
                height="80%"
                centered
                onCancel={() => {
                  setPreviewDocModal(false)
                  setPreviewDocUrl('')
                }}
                footer={null}
              >
                <iframe
                  src={previewDocUrl}
                  width="100%"
                  height="1000px"
                  title="previewDoc"
                ></iframe>
              </Modal>
            </>
          )}

          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => record.name && window.open(record.name, '_blank')}
          >
            下载
          </Button>
          {type === 'self' && (
            <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() =>
                  navigate(`update/${record.id}?learn_name=${learnName}`)
                }
              >
                编辑
              </Button>
              <Button
                type="link"
                size="large"
                className="danger-btn-link"
                onClick={() => handleRowDelete(record)}
              >
                删除
              </Button>
            </Permission>
          )}
        </Flex>
      ),
    },
  ] as ColumnsType<DataType>

  const { loading } = useAsync(async () => {
    await getTableData()
  })

  const getTableData = async (params: PageParams = pageParams) => {
    const res = await queryLearnResourceList({ ...params, id: id! })
    if (res.code !== 200001) {
      setTableData([])
      setTableTotal(0)
    } else {
      setTableData(res.data.list)
      setTableTotal(res.data.pager.total)
    }
  }

  const detailDataVoid = (ids: string[]) => {
    delLearnResources(ids).then((res) => {
      if (res.code !== 200001) {
        message.error(`删除失败： ${res.message}`)
      } else {
        message.success('删除成功')
        getTableData()
      }
    })
  }

  const handleBatchDelete = () => {
    if (selectRowKeys.length === 0) {
      message.warning('请选择要删除的数据')
      return
    }
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除选中数据?',
      centered: true,
      onOk() {
        detailDataVoid(selectRowKeys)
      },
    })
  }

  const handleRowDelete = (row: LearnResource) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      onOk() {
        detailDataVoid([row.id!])
      },
    })
  }

  const getPreviewDoc = async (id: string, url: string | undefined) => {
    if (!url) {
      message.error('无资料可预览')
    } else if (
      ['doc', 'docx', 'pdf', 'png', 'jpg', 'jpeg'].includes(
        url.split('.').pop()?.toLowerCase() as string
      )
    ) {
      const res = await service.get<ApiResponse<string>>(
        `/files/${id}/preview-url`
      )
      if (res.code !== 200001) {
        message.error(`获取失败： ${res.message}`)
      } else {
        setPreviewDocUrl(res.data)
        setPreviewDocModal(true)
      }
    } else {
      message.error('该类型文件暂不支持预览')
      return
    }
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '个人学习' },
          {
            title: '自学计划',
            href: '#',
            onClick: () => navigate(`/person/plan`),
          },
          { title: learnName },
        ]}
      />

      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          {type === 'self' && (
            <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
              <Button
                color="primary"
                variant="solid"
                size="large"
                className="primary-btn"
                onClick={() => navigate(`create?learn_name=${learnName}`)}
              >
                上传资料
              </Button>
              <Button
                color="danger"
                variant="filled"
                size="large"
                className="danger-btn-outlined"
                onClick={handleBatchDelete}
              >
                批量删除
              </Button>
            </Permission>
          )}
        </Flex>
        <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{
            type: 'checkbox',
            fixed: true,
            onChange: (selectedRowKeys) => {
              setSelectRowKeys(selectedRowKeys as string[])
            },
          }}
          total={tableTotal}
          onChange={(page, pageSize) => {
            const params = { ...pageParams, current: page, pageSize: pageSize }
            setPageParams(params)
            getTableData(params)
          }}
        ></CustomTable>
      </div>
    </div>
  )
}

export default PersonPlanList
