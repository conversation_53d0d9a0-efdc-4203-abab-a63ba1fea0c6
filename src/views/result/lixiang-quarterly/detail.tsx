import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  DatePicker,
  Form,
  Input,
  message,
  Spin,
} from 'antd'
import dayjs from 'dayjs'
import { useRef, useState } from 'react'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { useAsync } from 'react-use'
import {
  createQuarterly,
  getQuarterlyDetail,
  updateQuarterly,
} from '~/apis/result/lixiang-quarterly'
import { ApiResponse, service } from '~/lib/service'

const QuarterlyDetail = () => {
  const path = useLocation().pathname
  const status = path.split('/')[3]
  const { id } = useParams()

  const pageTitle = () => {
    switch (status) {
      case 'create':
        return '新建数据'
      case 'edit':
        return '编辑数据'
      case 'detail':
        return '查看数据'
      default:
        break
    }
  }

  const navigate = useNavigate()

  const [form] = Form.useForm()
  const fileInput = useRef<HTMLInputElement>(null)

  const [loading, setLoading] = useState(false)
  const [fileList, setFileList] = useState<
    { id: string; name: string; url: string }[]
  >([])

  const downloadFile = async (fileId: string, name: string) => {
    setLoading(true)
    const fileName = decodeURIComponent((name || '').split('/').pop() || 'file')
    try {
      const res = await service.get<Blob>(`/files/${fileId}/download`, {
        responseType: 'blob',
      })
      const blobUrl = window.URL.createObjectURL(new Blob([res]))
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)
    } catch (error) {
      message.error('下载失败: ' + JSON.stringify(error))
    } finally {
      setLoading(false)
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    message.open({
      key: 'upload',
      content: '正在上传...',
      duration: 0,
      type: 'loading',
    })
    try {
      const fileRes = await service.post<
        ApiResponse<{ id: string; name: string; url: string }>
      >(
        '/files',
        { file },
        { headers: { 'Content-Type': 'multipart/form-data' } }
      )
      if (fileRes.code !== 200001) {
        message.open({
          key: 'upload',
          content: '上传失败',
          type: 'error',
        })
        return
      }

      setFileList([fileRes.data])
      form.setFieldValue('publicationMaterials', [fileRes.data])
      message.open({ key: 'upload', content: '上传成功', type: 'success' })
    } catch (error) {
      message.open({
        key: 'upload',
        content: '上传失败' + JSON.stringify(error),
        type: 'error',
      })
    } finally {
      if (fileInput.current) {
        fileInput.current.value = ''
      }
    }
  }

  const handleQuarterlyPost = async (param?: Record<string, any>) => {
    if (param?.status !== '已创建') {
      try {
        await form.validateFields()
      } catch {
        message.error('请完善表单信息后再提交')
        return
      }
    }

    setLoading(true)

    const fieldsValue = form.getFieldsValue()

    const params = {
      ...fieldsValue,
      ...param,
      issueAt: fieldsValue.issueAt
        ? dayjs(fieldsValue.issueAt).format('YYYY-MM-DD')
        : '',
    }

    try {
      const res = id
        ? await updateQuarterly(id, params)
        : await createQuarterly(params)

      if (res.code !== 200001) {
        message.error(res.message)
      } else {
        navigate('/result/lixiang-quarterly')
      }
    } catch (error) {
      message.error(JSON.stringify(error))
    } finally {
      setLoading(false)
    }
  }

  const fetchQuarterlyDetail = async (quarterlyId: string) => {
    setLoading(true)
    try {
      const res = await getQuarterlyDetail(quarterlyId)

      if (res.code !== 200001) {
        return message.error(res.message)
      }

      const formData = { ...res.data, issueAt: dayjs(res.data.issueAt) }

      setFileList(formData.publicationMaterials)
      form.setFieldsValue(formData)
    } catch (error) {
      message.error(JSON.stringify(error))
    } finally {
      setLoading(false)
    }
  }

  useAsync(async () => {
    if (!id) return

    await fetchQuarterlyDetail(id)
  }, [id])

  return (
    <div className="h-full">
      <Breadcrumb
        separator=">"
        items={[
          { title: ' 成果转化' },
          { title: '《理响》季刊', href: '/result/lixiang-quarterly' },
          { title: pageTitle() },
        ]}
      />

      <Card title={pageTitle()} className="my-4">
        <Spin spinning={loading}>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            autoComplete="off"
            labelAlign="left"
            className="px-24"
            disabled={status === 'detail'}
          >
            <Form.Item
              label="刊物主题"
              required
              rules={[{ required: true, message: '请输入刊物主题' }]}
              name="subject"
            >
              <Input placeholder="请输入刊物主题" />
            </Form.Item>

            <Form.Item
              label="刊号"
              required
              rules={[{ required: true, message: '请输入刊号' }]}
              name="publicationNo"
            >
              <Input placeholder="请输入刊号" />
            </Form.Item>

            <Form.Item
              label="发刊时间"
              required
              rules={[{ required: true, message: '请输入发刊时间' }]}
              name="issueAt"
            >
              <DatePicker placeholder="请选择发刊时间" />
            </Form.Item>

            <Form.Item
              label="刊物资料"
              required
              rules={[{ required: true, message: '请上传刊物资料' }]}
              name="publicationMaterials"
            >
              {status !== 'detail' && (
                <>
                  <Button
                    type="primary"
                    onClick={() => {
                      if (!fileInput.current) return

                      fileInput.current.click()
                    }}
                  >
                    选择文件
                  </Button>
                  <div className="text-sm text-[#999999]">
                    支持类型：doc、docx、xls、pdf、ppt、pptx、png等
                  </div>
                </>
              )}

              {fileList.length > 0 &&
                fileList?.map((item: any) => (
                  <div key={item.id}>
                    <span>{item.name}</span>
                    <Button
                      type="link"
                      disabled={false}
                      onClick={() => window.open(item.url, '_blank')}
                    >
                      预览
                    </Button>
                    <Button
                      type="link"
                      disabled={false}
                      onClick={() => downloadFile(item.id, item.name)}
                    >
                      下载
                    </Button>
                  </div>
                ))}
            </Form.Item>

            {status !== 'detail' && (
              <>
                <Form.Item noStyle name="createdAt"></Form.Item>
                <Form.Item noStyle name="releaseAt"></Form.Item>
                <Form.Item noStyle name="updatedAt"></Form.Item>
              </>
            )}

            {status === 'detail' && (
              <>
                <Form.Item label="创建时间" name="createdAt">
                  <Input />
                </Form.Item>
                <Form.Item label="发布时间" name="releaseAt">
                  <Input />
                </Form.Item>
                <Form.Item label="最后更新时间" name="updatedAt">
                  <Input />
                </Form.Item>
              </>
            )}

            <Form.Item wrapperCol={{ span: 18, offset: 6 }}>
              <div className="flex justify-end gap-2">
                {status === 'create' && (
                  <>
                    <Button
                      onClick={() => navigate('/result/lixiang-quarterly')}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => handleQuarterlyPost({ status: '已创建' })}
                    >
                      保存草稿
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => handleQuarterlyPost({ status: '已发布' })}
                    >
                      保存并发布
                    </Button>
                  </>
                )}

                {status === 'edit' &&
                  form.getFieldValue('status') === '已创建' && (
                    <>
                      <Button
                        onClick={() => navigate('/result/lixiang-quarterly')}
                        loading={loading}
                      >
                        取消
                      </Button>
                      <Button
                        type="primary"
                        onClick={() =>
                          handleQuarterlyPost({ status: '已创建' })
                        }
                      >
                        保存草稿
                      </Button>
                      <Button
                        type="primary"
                        onClick={() =>
                          handleQuarterlyPost({ status: '已发布' })
                        }
                      >
                        保存并发布
                      </Button>
                    </>
                  )}

                {status === 'edit' &&
                  form.getFieldValue('status') === '已发布' && (
                    <>
                      <Button
                        onClick={() => navigate('/result/lixiang-quarterly')}
                        loading={loading}
                      >
                        取消
                      </Button>
                      <Button
                        type="primary"
                        onClick={() =>
                          handleQuarterlyPost({ status: '已发布' })
                        }
                      >
                        保存并更新
                      </Button>
                    </>
                  )}

                {status === 'detail' && (
                  <Button
                    disabled={false}
                    onClick={() => navigate('/result/lixiang-quarterly')}
                  >
                    返回
                  </Button>
                )}
              </div>
            </Form.Item>
          </Form>
        </Spin>

        <input
          type="file"
          className="hidden"
          ref={fileInput}
          accept=".doc, .docx, .xls, .pdf, .ppt, .pptx, .png, .zip, .tar, .jpg, .jpeg"
          onChange={handleFileChange}
        />
      </Card>
    </div>
  )
}

export default QuarterlyDetail
