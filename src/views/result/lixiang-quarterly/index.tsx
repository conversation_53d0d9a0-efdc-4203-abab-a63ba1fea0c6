import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Flex, Input, message, Popconfirm } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  deleteQuarterly,
  getQuarterlyList,
  updateQuarterly,
} from '~/apis/result/lixiang-quarterly'
import Permission from '~/components/permission'
import CustomTable, { DataType } from '~/components/table'
import { PageParams } from '~/models/common/pageParams'

export default function LiXiangQuarterly() {
  const navigate = useNavigate()

  const [tableData, setTableData] = useState<any[]>([])
  const [subjectParam, setSubjectParam] = useState('')

  const [total, setTotal] = useState(0)
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })

  const [listLoading, setListLoading] = useState(false)

  const getTableData = async (subject: string, page: PageParams) => {
    setListLoading(true)

    try {
      const res = await getQuarterlyList({
        ...page,
        subject,
      })

      if (res.code !== 200001) {
        message.error(res.message)
        setTableData([])
      } else {
        setTableData(res.data.list)
        setTotal(res.data.pager.total)
      }
    } catch (error) {
      message.error(JSON.stringify(error))
    } finally {
      setListLoading(false)
    }
  }

  const handleQuarterlyDelete = async (quarterlyId: string) => {
    setListLoading(true)

    try {
      const res = await deleteQuarterly(quarterlyId)
      if (res.code !== 200001) {
        message.error(res.message)
      } else {
        getTableData(subjectParam, pageParams)
      }
    } catch (error) {
      message.error(JSON.stringify(error))
    } finally {
      setListLoading(false)
    }
  }

  const hasNoEmptyFields = (record: Record<string, any>) => {
    delete record.releaseAt
    delete record.publicationMaterials
    return Object.values(record).some((value) => !value)
  }

  const handleQuarterlyPost = async (record: any) => {
    setListLoading(true)

    const { id, ...params } = record

    if (hasNoEmptyFields(params)) {
      message.error('有必填项未填，请补充后再发布')
      setListLoading(false)
      return
    }

    try {
      const res = await updateQuarterly(id, {
        ...params,
        status: '已发布',
      })
      if (res.code !== 200001) {
        message.error(res.message)
      } else {
        getTableData(subjectParam, pageParams)
      }
    } catch (error) {
      message.error(JSON.stringify(error))
    } finally {
      setListLoading(false)
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '刊物主题',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: '刊号',
      dataIndex: 'publicationNo',
      key: 'publicationNo',
    },
    {
      title: '发刊时间',
      dataIndex: 'issueAt',
      key: 'issueAt',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '发布时间',
      dataIndex: 'releaseAt',
      key: 'releaseAt',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 150,
      render: (_text, record) => {
        return (
          <Flex className="w-40 flex-wrap">
            <Button type="link" onClick={() => navigate(`detail/${record.id}`)}>
              查看
            </Button>

            <Permission
              roles={['qt_undertaker', 'sa']}
              codes={[]}
              companyId="00000000-0000-0000-0000-000000000000"
            >
              <Button type="link" onClick={() => navigate(`edit/${record.id}`)}>
                编辑
              </Button>

              {record.status !== '已发布' && (
                <Popconfirm
                  title="确认发布？"
                  onConfirm={() => handleQuarterlyPost(record)}
                >
                  <Button type="link">发布</Button>
                </Popconfirm>
              )}

              <Popconfirm
                title="确认删除？"
                onConfirm={() => handleQuarterlyDelete(record.id)}
              >
                <Button type="link" danger>
                  删除
                </Button>
              </Popconfirm>
            </Permission>
          </Flex>
        )
      },
    },
  ] as ColumnsType<DataType>

  useEffect(() => {
    getTableData('', { current: 1, pageSize: 10 })
  }, [])

  return (
    <div className="h-full">
      <Breadcrumb
        separator=">"
        items={[{ title: '成果转化' }, { title: '《理响》季刊' }]}
      />

      <Flex
        className="my-4 bg-white p-4"
        justify="space-between"
        align="center"
      >
        <Flex gap="large" justify="space-between" align="center">
          <span className="flex-shrink-0">刊物主题</span>
          <Input
            placeholder="请输入刊物主题"
            value={subjectParam}
            onChange={(e) => setSubjectParam(e.target.value)}
          />
        </Flex>

        <Flex gap="small">
          <Button
            color="primary"
            className="primary-btn"
            variant="solid"
            onClick={() =>
              getTableData(subjectParam, { current: 1, pageSize: 10 })
            }
          >
            查询
          </Button>
          <Button
            onClick={() => {
              setSubjectParam('')
              setPageParams({ current: 1, pageSize: 10 })
              getTableData('', { current: 1, pageSize: 10 })
            }}
          >
            重置
          </Button>
        </Flex>
      </Flex>

      <div className="mt-4 flex h-4/5 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          <Permission
            roles={['qt_undertaker', 'sa']}
            codes={[]}
            companyId="00000000-0000-0000-0000-000000000000"
          >
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="primary-btn"
              onClick={() => navigate('create')}
            >
              新增刊物
            </Button>
          </Permission>
        </Flex>

        <CustomTable
          loading={listLoading}
          columns={columns}
          dataSource={tableData}
          pagination={{
            onChange: (page, pageSize) => {
              const params = { ...pageParams, current: page, pageSize }
              setPageParams(params)
              getTableData(subjectParam, params)
            },
            total: total,
            current: pageParams.current,
            pageSize: pageParams.pageSize,
            showSizeChanger: true,
            showTotal: (total, range) =>
              `第 ${range[0]} - ${range[1]} 条 / 共 ${total} 条`,
          }}
          rowKey="id"
        ></CustomTable>
      </div>
    </div>
  )
}
