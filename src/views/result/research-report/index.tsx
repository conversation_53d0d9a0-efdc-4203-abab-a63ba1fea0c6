import { ExclamationCircleFilled } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Flex,
  Modal,
  message,
  DatePicker,
  Form,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'
import { useAsync } from 'react-use'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import { queryLearnList, delReport } from '~/apis/result/research-report'
// import { Learn } from '~/models/result/research-report'
import { PageParams } from '~/models/common/pageParams'
import { ApiResponse, service } from '~/lib/service'
import Permission from '~/components/permission'
import { useAuthStore } from '~/store/auth'

const ResearchReport: React.FC = () => {
  const [tableData, setTableData] = useState<any[]>([])
  const [rowData, setRowData] = useState<any>({})
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })
  const [tableTotal, setTableTotal] = useState(0)
  const navigate = useNavigate()
  const { confirm } = Modal
  const [submitTimeForm] = Form.useForm()

  const [deadlineModalVisible, setDeadlineModalVisible] = useState(false)
  const [tableRowStatus, setTableRowStatus] = useState<Record<string, any>>({})
  const [updateEndDateLoading, setUpdateEndDateLoading] = useState(false)
  const [listLoading, setListLoading] = useState(false)
  const { userInfo } = useAuthStore()

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const handleRemind = (record: any) => {
    const remind = async () => {
      message.open({
        type: 'loading',
        content: '请稍后...',
        duration: 0,
      })
      try {
        const res = await service.put<ApiResponse<unknown>>(
          `/achievement/report/msg/${record.id}`
        )
        if (res.code !== 200001) {
          message.destroy()
          message.open({
            type: 'error',
            content: res.message,
          })
          return
        }
        message.destroy()
        message.open({
          type: 'success',
          content: '操作成功',
        })
        getTableData(pageParams)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (err) {
        message.destroy()
        message.open({
          type: 'error',
          content: '操作失败',
        })
      }
    }
    if (record.end_at && dayjs(record.end_at).isBefore(dayjs())) {
      const dialog = confirm({
        title: '提醒提示',
        icon: <ExclamationCircleFilled />,
        content: '当前已超过提交时限，是否要更新提交时限?',
        centered: true,
        onOk() {
          dialog.destroy()
          getSubmissionTime(record)
        },
        async onCancel() {
          await remind()
        },
        okText: '修改',
        cancelText: '不修改',
        footer: (_, { CancelBtn, OkBtn }) => {
          return (
            <>
              <OkBtn />
              <CancelBtn />
              <Button
                onClick={() => {
                  dialog.destroy()
                }}
              >
                取消
              </Button>
            </>
          )
        },
      })
      return dialog
    }
    confirm({
      title: '提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认提醒该条数据?',
      centered: true,
      onOk: async () => {
        await remind()
      },
    })
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '调研主题',
      dataIndex: 'name',
      key: 'name',
      width: 170,
      align: 'left',
    },
    {
      title: '党组织名称',
      dataIndex: 'company',
      key: 'company',
      width: 170,
      align: 'left',
    },
    {
      title: '中心组成员',
      dataIndex: 'members',
      key: 'members',
      width: 180,
      align: 'left',
      render: (members: any) =>
        members.map((member: any) => member.full_name).join(', '),
    },
    {
      title: '调研报告',
      dataIndex: 'report',
      key: 'report',
      width: 200,
      align: 'left',
      render: (_, record: any) => (
        <div className="flex break-words">
          {record.report.map((report: any) => (
            <Button
              key={report.id}
              type="link"
              size="large"
              style={{ textDecoration: 'underline', whiteSpace: 'normal' }}
              onClick={() => onFilePreview(report.id)}
            >
              查看
            </Button>
          ))}
        </div>
      ),
    },
    {
      title: '调研简报',
      dataIndex: 'briefing',
      key: 'briefing',
      width: 200,
      align: 'left',
      render: (_, record: any) => (
        <div className="flex break-words">
          {record.briefing.map((briefing: any) => (
            <Button
              key={briefing.id}
              type="link"
              size="large"
              style={{ textDecoration: 'underline', whiteSpace: 'normal' }}
              onClick={() => onFilePreview(briefing.id)}
            >
              查看
            </Button>
          ))}
        </div>
      ),
    },
    {
      title: '材料提交截止时间',
      dataIndex: 'end_at',
      key: 'end_at',
      width: 200,
      align: 'left',
      render: (end_at: any) => end_at && dayjs(end_at).format('YYYY-MM-DD'),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      align: 'center',
      render: (_, record: any) => {
        const hasPermission =
          userInfo?.roles.includes('sa') ||
          userInfo?.roles.includes('qt_undertaker') ||
          record?.secretary?.some((u: any) => u.user_id === userInfo?.id)
        return (
          <Flex wrap justify="center">
            <Permission roles={['qt_undertaker']} codes={[]}>
              {record.end_at && (
                <Button
                  type="link"
                  size="large"
                  className="primary-btn-link"
                  onClick={() => handleRemind(record)}
                  disabled={!!record?.msg_at}
                >
                  {record.msg_at ? '已提醒' : '提醒'}
                </Button>
              )}

              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() => getSubmissionTime(record)}
              >
                设置提交时限
              </Button>
            </Permission>
            {hasPermission && (
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() => navigate(`update/${record.id}`)}
              >
                编辑
              </Button>
            )}

            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() => downloadFile(record)}
            >
              下载
            </Button>
            {hasPermission && (
              <Button
                type="link"
                size="large"
                className="danger-btn-link"
                onClick={() => handleRowDelete(record)}
                loading={tableRowStatus[record.id]?.deleting}
              >
                删除
              </Button>
            )}
          </Flex>
        )
      },
    },
  ] as ColumnsType<DataType>

  useAsync(async () => {
    await getTableData()
  })
  const getTableData = async (params: PageParams = pageParams) => {
    setListLoading(true)
    try {
      const res = await queryLearnList(params)
      if (res.code !== 200001) {
        setTableData([])
        setTableRowStatus({})
        setTableTotal(0)
      } else {
        setTableData(res.data.list)
        setTableRowStatus(
          (res.data.list || []).reduce((prev: any, curr: any) => {
            prev[curr.id] = {
              deleting: false,
            }
            return prev
          }, {})
        )
        setTableTotal(res.data.pager.total)
      }
    } finally {
      setListLoading(false)
    }
  }
  const getSubmissionTime = (row: any) => {
    setRowData(row)
    if (row.end_at) {
      submitTimeForm.setFieldsValue({ end_at: dayjs(row.end_at) })
    } else {
      submitTimeForm.resetFields()
    }
    setDeadlineModalVisible(true)
  }
  const onSubmitSuccess = async (values: any) => {
    setUpdateEndDateLoading(true)
    try {
      await service
        .put<
          ApiResponse<any>
        >(`/achievement/report/${rowData.id}`, { ...rowData, end_at: dayjs(values.end_at).format('YYYY-MM-DD HH:mm:ss') })
        .then((res) => {
          if (res.code !== 200001) return message.warning(res.message)
          setDeadlineModalVisible(false)
          message.success('设置成功')
          getTableData()
        })
    } finally {
      setUpdateEndDateLoading(false)
    }
  }

  const handleRowDelete = (row: any) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      onOk() {
        handleDel([row.id])
      },
    })
  }
  const handleDel = (ids: string[]) => {
    setTableRowStatus({
      ...tableRowStatus,
      ...ids.reduce((prev: any, curr: any) => {
        prev[curr] = {
          ...prev[curr],
          deleting: true,
        }
        return prev
      }, {}),
    })
    delReport(ids).then((res) => {
      if (res.code !== 200001) {
        message.error(`删除失败： ${res.message}`)
      } else {
        message.success('删除成功')
        getTableData()
      }
      setTableRowStatus({
        ...tableRowStatus,
        ...ids.reduce((prev: any, curr: any) => {
          prev[curr] = {
            ...prev[curr],
            deleting: false,
          }
          return prev
        }, {}),
      })
    })
  }
  const downloadFile = (row: any) => {
    const fileUrls = []
    if (row.report[0]) {
      fileUrls.push({ id: row.report[0].id, name: row.report[0].name })
    }
    if (row.briefing[0]) {
      fileUrls.push({ id: row.briefing[0].id, name: row.briefing[0].name })
    }
    fileUrls.forEach((item) => {
      service
        .get<ApiResponse<any>>(`/files/${item.id}/download`, {
          responseType: 'blob',
        })
        .then((res: any) => {
          const blobUrl = window.URL.createObjectURL(new Blob([res])) // 将获取的文件转化为blob格式
          const link = document.createElement('a') // 创建一个a标签用于下载
          link.href = blobUrl
          link.download = item.name.split('/').pop() // 设置下载文件名
          document.body.appendChild(link)
          link.click() // 点击下载
          document.body.removeChild(link) // 下载完成移除元素
          window.URL.revokeObjectURL(blobUrl) // 释放掉blob对象
          message.success('文件下载成功') // 提示下载成功
        })
    })
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '成果转化' }, { title: '专题调研' }]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          <Permission roles={['qt_undertaker']} codes={[]}>
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="primary-btn"
              onClick={() => navigate(`create`)}
            >
              新增数据
            </Button>
          </Permission>
        </Flex>
        <CustomTable
          loading={listLoading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={tableTotal}
          onChange={(page, pageSize) => {
            const params = { ...pageParams, current: page, pageSize: pageSize }
            setPageParams(params)
            getTableData(params)
          }}
        ></CustomTable>
      </div>

      <Modal
        title="请设定材料提交截止时间"
        open={deadlineModalVisible}
        centered
        destroyOnClose
        okButtonProps={{ loading: updateEndDateLoading }}
        onOk={() => submitTimeForm.submit()}
        onCancel={() => setDeadlineModalVisible(false)}
      >
        <Form
          form={submitTimeForm}
          name="submitTimeForm"
          initialValues={{ end_at: '' }}
          clearOnDestroy
          onFinish={onSubmitSuccess}
          autoComplete="off"
          labelAlign="left"
        >
          <Form.Item
            label="截止时间"
            name="end_at"
            rules={[{ required: true, message: '请选择截止时间' }]}
          >
            <DatePicker
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
              placeholder="请选择截止时间"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ResearchReport
