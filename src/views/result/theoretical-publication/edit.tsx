import {
  Button,
  Form,
  Input,
  Select,
  message,
  Breadcrumb,
  DatePicker,
  Flex,
  Spin,
} from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import type { UploadFile } from 'antd/es/upload/interface'
import { useEffect, useMemo, useState } from 'react'
import dayjs from 'dayjs'
import FileUpload from '~/components/upload-file'
import { ApiResponse, service } from '~/lib/service'
interface Learn {
  key: string
  company_id: string
  created_at: string
  creator: string
  creator_id: string
  deleted_at: string
  dept_id: string
  id: string
  name: string
  updated_at: string
  full_name: string
  user_id: string
}
interface FormValues {
  researchTopic: string
  centralGroupMembers: string[]
  topic: string[]
  publishingMedia: string[]
  articleLink: string[]
  researchReport: UploadFile[]
  researchBriefing: UploadFile[]
  name: string
  author: any
  publish: any
  release_at: any
  secretary: any
  link: any
  attachment: UploadFile[]
}

const TheoreticalPublicationEdit: React.FC = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const [optionsFromAPI, setOptionsFromAPI] = useState<any[]>([])
  const { id } = useParams()
  const [saveLoading] = useState(false)
  const [idData, setIdData] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedUserInfos, setSelectedUserInfos] = useState<any[]>([])

  const secretary = Form.useWatch('secretary', form)

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const response = await service.get<ApiResponse<Learn[]>>('/users')
        if (response.code == 200001) {
          const processedItems = response.data.map((item) => {
            const newItem = { value: item.id, label: item.full_name }
            return newItem
          })
          setOptionsFromAPI(processedItems)
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }
    const fetchDataForEdit = async () => {
      if (id) {
        setLoading(true)
        try {
          const response = await service.get<ApiResponse<any>>(
            `/achievement/article/${id}`
          )
          if (response.code === 200001) {
            const data = response.data
            setIdData(data)
            form.setFieldsValue({
              name: data.name,
              author: data.author,
              publish: data.publish,
              release_at: dayjs(data.release_at),
              link: data.link,
              secretary: data.secretary.map(
                (secretary: { user_id: any }) => secretary.user_id
              ),
              attachment: data.attachment.map((file: any) => ({
                id: file.id,
                name: file.name,
                url: file.url,
              })),
            })
          } else {
            message.error('获取数据失败')
          }
        } catch (error) {
          console.error('Error fetching data for edit:', error)
          message.error('获取数据过程中出现错误')
        } finally {
          setLoading(false)
        }
      }
    }
    fetchOptions()
    fetchDataForEdit()
  }, [id, form])

  useEffect(() => {
    if (!secretary || secretary.length === 0) return
    const getUsers = async () => {
      const response = await service.post<ApiResponse<any[]>>(
        '/users/userOptionByIdV2',
        {
          ids: secretary,
        }
      )
      if (response.code == 200001) {
        setSelectedUserInfos(
          (response.data || []).map((item: any) => {
            return {
              value: item.id,
              label: item.full_name,
            }
          })
        )
      } else {
        message.error('获取人员列表失败')
      }
    }
    getUsers()
  }, [secretary])

  const usersOption = useMemo<any[]>(() => {
    return Array.from(
      new Map(
        [...selectedUserInfos, ...optionsFromAPI].map((item) => [
          item.value,
          item,
        ])
      ).values()
    )
  }, [optionsFromAPI, selectedUserInfos])

  // 处理表单提交
  const handleSubmit = async (values: FormValues) => {
    let submitData: any
    if (id) {
      submitData = {
        ...idData,
        name: values.name,
        author: values.author,
        publish: values.publish,
        release_at: values.release_at,
        secretary: values.secretary.map((userId: string) => ({
          user_id: userId,
        })),
        link: values.link,
        attachment: values.attachment,
      }
    } else {
      submitData = {
        name: values.name,
        author: values.author,
        publish: values.publish,
        release_at: values.release_at,
        secretary: values.secretary.map((userId: string) => ({
          user_id: userId,
        })),
        link: values.link,
        attachment: values.attachment,
      }
    }

    try {
      let response
      if (id) {
        // 使用 PUT 方法更新数据
        response = await service.put<ApiResponse<any>>(
          `/achievement/article/${id}`,
          submitData
        )
      } else {
        // 使用 POST 方法创建新数据
        response = await service.post<ApiResponse<any>>(
          '/achievement/article',
          submitData
        )
      }
      if (response.code === 200001) {
        message.success('保存成功')
        navigate('/result/theoretical-publication')
      } else {
        message.error('保存不成功')
      }
    } catch (error) {
      console.error('Error submitting data:', error)
      message.error('提交过程中出现错误')
    }

    // await service.post('/api/research-reports', submitData)
    // message.success('保存成功')
    // navigate('/result/theoretical-publication')
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          { title: '成果转化' },
          {
            title: '理论文章',
            href: '#',
            onClick: () => navigate(`/result/theoretical-publication`),
          },
          { title: id ? '编辑' : '新增' },
        ]}
      />
      {loading ? (
        <div className="flex h-0 flex-1 items-center justify-center">
          <Spin spinning={loading} />
        </div>
      ) : (
        <div className="mt-4 flex h-0 w-full flex-1 flex-col items-center bg-white">
          <p className="w-full border-b border-gray-200 p-4 font-semibold">
            {id ? '编辑数据' : '新增数据'}
          </p>
          <Form
            form={form}
            onFinish={handleSubmit}
            labelCol={{ span: 2 }}
            labelAlign="right"
            className="w-3/4 min-w-[600px] flex-1 overflow-auto p-12"
          >
            <Form.Item
              label="文章题目"
              name="name"
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="作者"
              name="author"
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="刊发媒体"
              name="publish"
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="发布时间"
              name="release_at"
              rules={[{ required: true, message: '请输入' }]}
            >
              <DatePicker
                placeholder="请选择年月日"
                format="YYYY-MM-DD"
                style={{ width: '100%' }}
              />
            </Form.Item>
            <Form.Item
              label="学习秘书"
              name="secretary"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择"
                options={usersOption}
              />
            </Form.Item>
            <Form.Item
              label="文章链接"
              name="link"
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item name="attachment" label="附件">
              <FileUpload></FileUpload>
            </Form.Item>
          </Form>
          <Flex justify="end" gap="small" className="w-full p-4">
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="secondary-btn-outlined"
              onClick={() => navigate('/result/theoretical-publication')}
            >
              取消
            </Button>
            <Button
              color="primary"
              loading={saveLoading}
              variant="solid"
              size="large"
              className="danger-btn"
              onClick={() => form.submit()}
            >
              保存
            </Button>
          </Flex>
        </div>
      )}
    </div>
  )
}

export default TheoreticalPublicationEdit
