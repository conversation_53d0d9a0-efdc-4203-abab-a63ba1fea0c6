import { ExclamationCircleFilled } from '@ant-design/icons'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Flex,
  Modal,
  message,
  DatePicker,
  Form,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'
import { useAsync } from 'react-use'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import {
  queryArticleList,
  delArticle,
} from '~/apis/result/theoretical-publication'
import { PageParams } from '~/models/common/pageParams'
import { ApiResponse, service } from '~/lib/service'
import Permission from '~/components/permission'
import { useAuthStore } from '~/store/auth'

const TheoreticalPublication: React.FC = () => {
  const [tableData, setTableData] = useState<any[]>([])
  const [rowData] = useState<any>({})
  const [pageParams, setPageParams] = useState<PageParams>({
    current: 1,
    pageSize: 10,
  })
  const [tableTotal, setTableTotal] = useState(0)
  const navigate = useNavigate()
  const { confirm } = Modal
  const [submitTimeForm] = Form.useForm()

  const [deadlineModalVisible, setDeadlineModalVisible] = useState(false)
  const [tableRowStatus, setTableRowStatus] = useState<Record<string, any>>({})
  const [updateEndDateLoading, setUpdateEndDateLoading] = useState(false)
  const [listLoading, setListLoading] = useState(false)
  const { userInfo } = useAuthStore()

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '文章主题',
      dataIndex: 'name',
      key: 'name',
      width: 170,
      align: 'left',
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 100,
      align: 'left',
    },
    {
      title: '党组织名称',
      dataIndex: 'company_name',
      key: 'company_name',
      width: 160,
      align: 'left',
    },
    {
      title: '发布时间',
      dataIndex: 'release_at',
      key: 'release_at',
      width: 120,
      align: 'left',
      render: (value) => (value ? dayjs(value).format('YYYY-MM-DD') : null),
    },
    {
      title: '刊发媒体',
      dataIndex: 'publish',
      key: 'publish',
      width: 150,
      align: 'left',
    },
    {
      title: '文章链接',
      dataIndex: 'link',
      key: 'link',
      width: 200,
      align: 'center',
      render: (_, record: any) => (
        <div
          className="flex break-words"
          style={{ width: '200px', wordBreak: 'break-all' }}
        >
          <Button
            type="link"
            size="large"
            style={{
              textDecoration: 'underline',
              whiteSpace: 'normal',
              width: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
            onClick={() => window.open(record.link, '_blank')}
          >
            查看
          </Button>
        </div>
      ),
    },
    {
      title: '附件',
      dataIndex: 'attachment',
      key: 'attachment',
      width: 200,
      align: 'left',
      render: (_, record: any) => (
        <div className="flex break-words">
          {record.attachment.map((report: any) => (
            <Button
              key={report.id}
              type="link"
              size="large"
              style={{ textDecoration: 'underline', whiteSpace: 'normal' }}
              onClick={() => onFilePreview(report.id)}
            >
              查看
            </Button>
          ))}
        </div>
      ),
    },
    // {
    //   title: '材料提交截止时间',
    //   dataIndex: 'end_at',
    //   key: 'end_at',
    //   width: 200,
    //   align: 'left',
    // },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      align: 'left',
      fixed: 'right',
      render: (_, record: any) => {
        const hasPermission =
          userInfo?.roles.includes('sa') ||
          userInfo?.roles.includes('qt_undertaker') ||
          record?.secretary?.some((u: any) => u.user_id === userInfo?.id)
        return (
          <Flex wrap justify="center">
            {/* <Permission roles={['qt_undertaker']} codes={[]}>
            <Button type="link" size="large" className="primary-btn-link">
              提醒
            </Button>
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              // onClick={() => setDeadlineModalVisible(true)}
              onClick={() => getSubmissionTime(record)}
            >
              提交时间
            </Button>
          </Permission> */}
            {hasPermission && (
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() => navigate(`update/${record.id}`)}
              >
                编辑
              </Button>
            )}

            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() => downloadFile(record)}
            >
              下载
            </Button>
            {hasPermission && (
              <Button
                type="link"
                size="large"
                className="danger-btn-link"
                onClick={() => handleRowDelete(record)}
                loading={tableRowStatus[record.id]?.deleting}
              >
                删除
              </Button>
            )}
          </Flex>
        )
      },
    },
  ] as ColumnsType<DataType>

  useAsync(async () => {
    await getTableData()
  })
  const getTableData = async (params: PageParams = pageParams) => {
    setListLoading(true)
    try {
      const res = await queryArticleList(params)
      if (res.code !== 200001) {
        setTableData([])
        setTableRowStatus({})
        setTableTotal(0)
      } else {
        setTableData(res.data.list)
        setTableRowStatus(
          (res.data.list || []).reduce((prev: any, curr: any) => {
            prev[curr.id] = {
              deleting: false,
            }
            return prev
          }, {})
        )
        setTableTotal(res.data.pager.total)
      }
    } finally {
      setListLoading(false)
    }
  }
  // const getSubmissionTime = (row: any) => {
  //   setRowData(row)
  //   if (row.end_at) {
  //     submitTimeForm.setFieldsValue({ end_at: dayjs(row.end_at) })
  //   } else {
  //     submitTimeForm.resetFields()
  //   }
  //   setDeadlineModalVisible(true)
  // }
  const onSubmitSuccess = (values: any) => {
    setUpdateEndDateLoading(true)
    service
      .put<
        ApiResponse<any>
      >(`/achievement/article/${rowData.id}`, { ...rowData, end_at: dayjs(values.end_at).format('YYYY-MM-DD HH:mm:ss') })
      .then((res) => {
        if (res.code === 200001) {
          setDeadlineModalVisible(false)
          message.success('设置成功')
          getTableData()
        }
        setUpdateEndDateLoading(false)
      })
  }

  const handleRowDelete = (row: any) => {
    confirm({
      title: '删除提示',
      icon: <ExclamationCircleFilled />,
      content: '是否确认删除该条数据?',
      centered: true,
      onOk() {
        handleDel([row.id])
      },
    })
  }
  const handleDel = (ids: string[]) => {
    setTableRowStatus({
      ...tableRowStatus,
      ...ids.reduce((prev: any, curr: any) => {
        prev[curr] = {
          ...prev[curr],
          deleting: true,
        }
        return prev
      }, {}),
    })
    delArticle(ids).then((res) => {
      if (res.code !== 200001) {
        message.error(`删除失败： ${res.message}`)
      } else {
        message.success('删除成功')
        getTableData()
      }
      setTableRowStatus({
        ...tableRowStatus,
        ...ids.reduce((prev: any, curr: any) => {
          prev[curr] = {
            ...prev[curr],
            deleting: false,
          }
          return prev
        }, {}),
      })
    })
  }

  const downloadFile = (row: any) => {
    service
      .get<ApiResponse<any>>(`/files/${row.attachment[0].id}/download`, {
        responseType: 'blob',
      })
      .then((res: any) => {
        const url = window.URL.createObjectURL(new Blob([res])) // 将获取的文件转化为blob格式
        const link = document.createElement('a') // 创建一个a标签用于下载
        link.href = url
        link.download = row.attachment[0].name // 设置下载文件名
        document.body.appendChild(link)
        link.click() // 点击下载
        document.body.removeChild(link) // 下载完成移除元素
        window.URL.revokeObjectURL(url) // 释放掉blob对象
        message.success('文件下载成功') // 提示下载成功
      })
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[{ title: '成果转化' }, { title: '理论文章' }]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <Flex className="mb-4" gap="small" wrap>
          <Permission roles={['qt_undertaker']} codes={[]}>
            <Button
              color="primary"
              variant="solid"
              size="large"
              className="primary-btn"
              onClick={() => navigate(`create`)}
            >
              新增数据
            </Button>
          </Permission>
        </Flex>
        <CustomTable
          loading={listLoading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={tableTotal}
          onChange={(page, pageSize) => {
            const params = { ...pageParams, current: page, pageSize: pageSize }
            setPageParams(params)
            getTableData(params)
          }}
        ></CustomTable>
      </div>

      <Modal
        title="请设定材料提交截止时间"
        open={deadlineModalVisible}
        centered
        destroyOnClose
        okButtonProps={{ loading: updateEndDateLoading }}
        onOk={() => submitTimeForm.submit()}
        onCancel={() => setDeadlineModalVisible(false)}
      >
        <Form
          form={submitTimeForm}
          name="submitTimeForm"
          initialValues={{ end_at: '' }}
          clearOnDestroy
          onFinish={onSubmitSuccess}
          autoComplete="off"
          labelAlign="left"
        >
          <Form.Item
            label="截止时间"
            name="end_at"
            rules={[{ required: true, message: '请选择截止时间' }]}
          >
            <DatePicker
              format="YYYY-MM-DD"
              style={{ width: '100%' }}
              placeholder="请选择截止时间"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TheoreticalPublication
