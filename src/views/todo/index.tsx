import { Badge, Breadcrumb, Tabs } from 'antd'
import { useQueryState } from 'nuqs'
import { Meeting } from './meeting'
import { Leaves } from './leaves'
import { Report } from './report'
import { Research } from './research'
import { MeetingWatch } from './meeting-watch'
import { useMeetingStore } from '~/store/meeting'

export function Todo() {
  const [type, setType] = useQueryState('tab', {
    defaultValue: 'meeting',
  })
  const meetingStore = useMeetingStore()
  return (
    <div className="flex h-full flex-col gap-4">
      <Breadcrumb
        separator=">"
        items={[{ title: '我的待办' }, { title: '列表' }]}
      />
      <div className="flex h-0 flex-1 flex-col">
        <Tabs
          type="card"
          items={[
            {
              key: 'meeting',
              label: (
                <div className="flex items-center gap-2">
                  中心组会议
                  {meetingStore.count > 0 && (
                    <Badge count={meetingStore.count} />
                  )}
                </div>
              ),
            },
            {
              key: 'leaves',
              label: (
                <div className="flex items-center gap-2">
                  会议请假审批
                  {meetingStore.leavesCount > 0 && (
                    <Badge count={meetingStore.leavesCount} />
                  )}
                </div>
              ),
            },
            {
              key: 'report',
              label: (
                <div className="flex items-center gap-2">
                  年度报告
                  {meetingStore.reportCount > 0 && (
                    <Badge count={meetingStore.reportCount} />
                  )}
                </div>
              ),
            },
            {
              key: 'research',
              label: (
                <div className="flex items-center gap-2">
                  专题调研
                  {meetingStore.researchCount > 0 && (
                    <Badge count={meetingStore.researchCount} />
                  )}
                </div>
              ),
            },
            {
              key: 'sit-in-on',
              label: (
                <div className="flex items-center gap-2">
                  列席旁听
                  {meetingStore.meetingWatchCount > 0 && (
                    <Badge count={meetingStore.meetingWatchCount} />
                  )}
                </div>
              ),
            },
          ]}
          defaultActiveKey={type}
          onChange={(key) => setType(key)}
        />
        <div className="h-0 flex-1 bg-white p-4">
          {type === 'meeting' && <Meeting />}
          {type === 'leaves' && <Leaves />}
          {type === 'report' && <Report />}
          {type === 'research' && <Research />}
          {type === 'sit-in-on' && <MeetingWatch />}
        </div>
      </div>
    </div>
  )
}
