import { Button, message, Modal } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

export const MeetingWatch = () => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<DataType[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [tableLoading, setTableLoading] = useState(false)
  const { userInfo } = useAuthStore()
  const navigate = useNavigate()
  const [viewRequirementVisible, setViewRequirementVisible] = useState(false)

  const [requirement, setRequirement] = useState('')

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '列席旁听会议名称',
      dataIndex: 'meeting_name',
      key: 'meeting_name',
      width: 170,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '发起人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
      align: 'left',
    },
    {
      title: '承办人',
      dataIndex: 'under_takers',
      key: 'under_takers',
      width: 100,
      align: 'left',
      render: (val) => {
        if (!val || val.length === 0) return null
        return val.map((item: any) => item.under_taker_name).join(',')
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 200,
      align: 'left',
    },
    {
      title: '会议开始时间',
      dataIndex: 'begin_time',
      key: 'begin_time',
      width: 200,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '会议结束时间',
      dataIndex: 'end_time',
      key: 'end_time',
      width: 200,
      align: 'left',
      render: (val) => {
        if (!val) return <span className="text-red-600">待填写</span>
        return val
      },
    },
    {
      title: '发起时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 200,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 260,
      align: 'center',
      fixed: 'right',
      render: (_, record: any) => (
        <div className="flex justify-center" style={{ flexWrap: 'wrap' }}>
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => {
              setViewRequirementVisible(true)
              setRequirement(record.requirement)
            }}
          >
            列席旁听要求
          </Button>
          {record.operations.fill_data && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() =>
                navigate(`/examine/sit-in-on/meetingInformation/${record.id}`)
              }
            >
              填写会议信息
            </Button>
          )}
          {record.operations.fill_check && !record.check_table_time && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() =>
                navigate(
                  `/examine/sit-in-on/attendanceAuditChecklist/${record.id}`
                )
              }
            >
              填写检查表
            </Button>
          )}

          {record.status === '已结束' && (
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() => {
                const underTakerIds = (record.under_takers || []).map(
                  (item: any) => item.under_taker_id
                )
                navigate(`/examine/sit-in-on/reformRecord/${record.id}`, {
                  state: {
                    role:
                      userInfo?.id === record.creator_id
                        ? 'creator'
                        : userInfo?.id === record.under_taker_id ||
                            underTakerIds.includes(userInfo?.id)
                          ? 'under_taker'
                          : 'user',
                  },
                })
              }}
            >
              整改台账
            </Button>
          )}

          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`/examine/sit-in-on/detail/${record.id}`)}
          >
            查看详情
          </Button>
        </div>
      ),
    },
  ] as ColumnsType<DataType>

  const getTableData = async () => {
    setTableLoading(true)

    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/todo/meetingWatch', { params: pagination })
      if (res.code !== 200001) {
        setTableData([])
        setTableTotal(0)
        message.error(res.message)
        return
      } else {
        setTableData(res.data!.list)
        setTableTotal(res.data!.pager.total)
      }
    } catch (error) {
      message.error('获取数据失败')
      console.log(error)
    } finally {
      setTableLoading(false)
    }
  }

  useEffect(() => {
    getTableData()
  }, [pagination])

  return (
    <div className="flex h-full flex-col">
      <CustomTable
        loading={tableLoading}
        columns={columns}
        dataSource={tableData}
        total={tableTotal}
        pagination={pagination}
        onChange={(page, pageSize) => {
          setPagination({
            ...pagination,
            current: page,
            pageSize,
          })
        }}
      />
      <Modal
        title="列席旁听要求"
        open={viewRequirementVisible}
        onCancel={() => {
          setViewRequirementVisible(false)
          setRequirement('')
        }}
        footer={null}
        centered
      >
        <div className="mt-4 border-t border-gray-200 pt-4">
          <div className="text-gray-700">{requirement}</div>
        </div>
      </Modal>
    </div>
  )
}
