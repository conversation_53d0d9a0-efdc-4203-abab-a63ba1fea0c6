import { ExclamationCircleOutlined } from '@ant-design/icons'
import { Button, Flex, Modal, Select, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { useAsync } from 'react-use'
import { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import CustomTable, { DataType } from '~/components/table'
import Permission from '~/components/permission'
import { startAttendMeetingAction } from '~/apis/group/meeting'
import { Meeting as MeetingModel } from '~/models/group/meeting'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'
import { getMeetingName } from '~/lib/utils'
export const Meeting: React.FC = () => {
  const navigate = useNavigate()
  const { confirm } = Modal
  const [pageParams, setPageParams] = useState<any>({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<MeetingModel[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [messageApi, contextHolder] = message.useMessage()
  const { userInfo } = useAuthStore()
  const [listLoading, setListLoading] = useState(false)
  const [showShareModal, setShowShareModel] = useState(false)
  const [shareMeetingId, setShareMeetingId] = useState('')
  const [shareLoading, setShareLoading] = useState(false)
  const [shareCompanyId, setShareCompanyId] = useState<string[]>([])
  const [shareCompanyList, setShareCompanyList] = useState<any[]>([])

  const exportMeeting = async (id: string, name: string) => {
    messageApi.open({
      key: 'exporting',
      type: 'loading',
      content: '正在导出...',
    })
    try {
      const res = await service.get<Blob>(`/export/meetings/${id}`, {
        responseType: 'blob',
      })
      if (!res || !res.size) {
        throw new Error('文件下载失败')
      }
      const blobUrl = window.URL.createObjectURL(
        new Blob([res], { type: 'application/zip' })
      )
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = `${name}-请假单.zip`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(blobUrl)

      messageApi.open({
        key: 'exporting',
        type: 'success',
        content: '导出成功!',
        duration: 2,
      })
    } catch {
      messageApi.open({
        key: 'exporting',
        type: 'error',
        content: '导出失败!',
        duration: 2,
      })
    }
  }

  const handleShareMeeting = async () => {
    if (!shareCompanyId) return message.error('请选择党组织')
    setShareLoading(true)
    try {
      const res = await service.post<ApiResponse<unknown>>(
        `/meetings/${shareMeetingId}/share`,
        {
          company: shareCompanyId,
        }
      )
      if (res.code !== 200001) {
        return message.error(res.message)
      }
      message.success('分享成功')
      setShareLoading(false)
      setShowShareModel(false)
    } catch (error) {
      console.error(error)
      setShareLoading(false)
    }
  }

  useEffect(() => {
    const getData = async () => {
      const res = await service.get<ApiResponse<[]>>('/orgs/son')
      if (res.code !== 200001) return
      setShareCompanyList(res.data || [])
    }
    getData()
  }, [])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_, _record, index) => index + 1,
    },
    {
      title: '会议名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'left',
      render: (_, record) => getMeetingName(record),
    },
    {
      title: '会议时间',
      dataIndex: 'start_at',
      key: 'start_at',
      width: 180,
      align: 'left',
      render: (value) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '会议地点',
      dataIndex: 'room',
      key: 'room',
      width: 150,
      align: 'left',
    },
    {
      title: '当前状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'left',
      render: (value) => {
        switch (value) {
          case 'created':
            return '已创建'
          case 'submitted':
            return '提交审核'
          case 'approving':
            return '审核中'
          case 'rejected':
            return '已驳回'
          case 'approved':
            return '审核通过'
          case 'confirming':
            return '参会确认中'
          case 'confirm':
            return '参会确认'
          case 'wait_publish':
            return '待发布'
          case 'publishing':
            return '发布中'
          case 'published':
            return '已发布'
          case 'publish_rejected':
            return '发布驳回'
          case 'wait_start':
            return '待开始'
          case 'started':
            return '进行中'
          case 'ended':
            return '已结束'
          case 'archived':
            return '已归档'
          default:
            return value
        }
      },
    },
    {
      title: '下一步操作',
      dataIndex: 'next',
      key: 'next',
      width: 150,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      align: 'left',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 220,
      fixed: 'right',
      align: 'center',
      render: (_, record: MeetingModel) => {
        const showEditButton =
          ['created', 'rejected', 'approved', 'confirming', 'confirm'].includes(
            record.status as string
          ) &&
          (userInfo?.roles?.includes('qt_undertaker') ||
            userInfo?.permission_codes?.includes('LearnManage') ||
            record?.staff?.find((u) => u.user_id === userInfo?.id))
        const showHHSC =
          ['ended'].includes(record.status as string) &&
          (userInfo?.roles?.includes('qt_undertaker') ||
            userInfo?.permission_codes?.includes('LearnManage') ||
            record?.staff?.find((u) => u.user_id === userInfo?.id))
        const showExportQJ =
          ['ended'].includes(record.status as string) &&
          (userInfo?.roles?.includes('qt_undertaker') ||
            userInfo?.permission_codes?.includes('LearnManage') ||
            record?.staff?.find((u) => u.user_id === userInfo?.id))
        return (
          <Flex justify="center" wrap>
            {showEditButton && (
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() => navigate(`/group/meeting/update/${record.id}`)}
              >
                编辑
              </Button>
            )}
            <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
              {record.status === 'ended' && (
                <Button
                  type="link"
                  size="large"
                  className="primary-btn-link"
                  onClick={() => {
                    setShowShareModel(true)
                    setShareMeetingId(record.id!)
                  }}
                >
                  分享会议
                </Button>
              )}
              {record.status === 'approved' && (
                <Button
                  type="link"
                  size="large"
                  className="primary-btn-link"
                  onClick={() => handleStartAttendMeetingAction(record.id!)}
                >
                  发起参会确认
                </Button>
              )}
              {record.status === 'wait_publish' && (
                <Button
                  type="link"
                  size="large"
                  className="primary-btn-link"
                  onClick={() =>
                    navigate(`/group/meeting/release/${record.id}`)
                  }
                >
                  发布会议
                </Button>
              )}
            </Permission>
            {/* <Permission roles={['xt_undertaker']} codes={[]}>
            {(record.status === 'confirming' ||
              record.status === 'wait_publish') && (
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() => navigate(`/group/meeting/supplement/${record.id}?type=bcxtcl`)}
              >
                补充协同材料
              </Button>
            )}
          </Permission> */}
            {showHHSC && (
              <Button
                type="link"
                size="large"
                className="primary-btn-link"
                onClick={() =>
                  navigate(`/group/meeting/supplement/${record.id}?type=hhsc`)
                }
              >
                会后上传
              </Button>
            )}
            {/* <Permission roles={['qt_undertaker']} codes={['LearnManage']}>
            {record.status === 'ended' && (
              <Button type="link" size="large" className="primary-btn-link">
                会议归档
              </Button>
            )}
          </Permission> */}
            {['wait_start', 'started', 'ended'].includes(
              record.status as string
            ) &&
              record.is_qj &&
              showExportQJ && (
                <Button
                  type="link"
                  size="large"
                  className="primary-btn-link"
                  onClick={() => {
                    exportMeeting(record.id!, record.name!)
                  }}
                >
                  导出请假单
                </Button>
              )}
            <Button
              type="link"
              size="large"
              className="primary-btn-link"
              onClick={() => navigate(`/group/meeting/${record.id}`)}
            >
              查看详情
            </Button>
          </Flex>
        )
      },
    },
  ] as ColumnsType<DataType>

  useAsync(async () => {
    await getTableData()
  })

  const getTableData = async (params: any = pageParams) => {
    setListLoading(true)
    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/todo/meeting', { params })
      if (res.code !== 200001) {
        setTableData([])
        setTableTotal(0)
      } else {
        setTableData(res.data.list)
        setTableTotal(res.data.pager.total)
      }
    } finally {
      setListLoading(false)
    }
  }

  const handleStartAttendMeetingAction = async (id: string) => {
    confirm({
      title: `确定发起参会确认吗？`,
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        startAttendMeetingAction(id).then((res) => {
          if (res.code !== 200001) {
            message.error(`发起参会确认失败：${res.message}`)
          } else {
            message.success('发起参会确认成功')
            getTableData()
          }
        })
      },
    })
  }

  return (
    <div className="flex h-full flex-col">
      <CustomTable
        loading={listLoading}
        columns={columns}
        dataSource={tableData}
        rowSelection={{
          type: 'checkbox',
          fixed: true,
        }}
        total={tableTotal}
        onChange={(page, pageSize) => {
          const params = { ...pageParams, current: page, pageSize: pageSize }
          setPageParams(params)
          getTableData(params)
        }}
      ></CustomTable>
      <Modal
        centered
        width={400}
        title="会议分享给下级党组织"
        open={showShareModal}
        onOk={handleShareMeeting}
        onCancel={() => {
          setShowShareModel(true)
          setShowShareModel(false)
        }}
        okButtonProps={{ loading: shareLoading }}
      >
        <div className="flex items-center gap-2">
          <div className="flex-shrink-0 flex-grow-0">请选择党组织</div>
          <div className="w-0 flex-1">
            <Select
              mode="multiple"
              className="w-full"
              options={shareCompanyList.map((item) => ({
                value: item.name,
                label: item.name,
              }))}
              onChange={(val) => {
                setShareCompanyId(val)
              }}
            />
          </div>
        </div>
      </Modal>
      {contextHolder}
    </div>
  )
}
