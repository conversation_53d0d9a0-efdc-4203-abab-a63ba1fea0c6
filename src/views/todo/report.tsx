import { Button, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useEffect, useRef, useState } from 'react'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'

export const Report = () => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<DataType[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [tableLoading, setTableLoading] = useState(false)

  const fileInput = useRef<HTMLInputElement>(null)
  const [uploadId, setUploadId] = useState('')
  const [messageApi, contextHolder] = message.useMessage()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      width: 100,
    },
    {
      title: '党组织名称',
      dataIndex: 'org_name',
      key: 'org_name',
      width: 200,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
    },
    {
      title: '报告提交截止时间',
      dataIndex: 'end_at',
      key: 'end_at',
      width: 200,
      render: (val: string) => {
        if (val && val.startsWith('000')) return null
        return val
      },
    },
    {
      title: '提醒人',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (_, record) => {
        return (
          <Button
            type="link"
            size="large"
            style={{ textDecoration: 'underline' }}
            className="primary-btn-link"
            onClick={() => {
              if (!fileInput.current) return
              setUploadId(record?.data_id)
              fileInput.current.click()
            }}
          >
            上传报告
          </Button>
        )
      },
    },
  ] as ColumnsType<DataType>

  const getTableData = async () => {
    setTableLoading(true)

    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/todo/yearReport', { params: pagination })
      if (res.code !== 200001) {
        setTableData([])
        setTableTotal(0)
        message.error(res.message)
        return
      } else {
        setTableData(res.data!.list)
        setTableTotal(res.data!.pager.total)
      }
    } catch (error) {
      message.error('获取数据失败')
      console.log(error)
    } finally {
      setTableLoading(false)
    }
  }

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    messageApi.open({
      key: 'upload',
      type: 'loading',
      content: '正在上传...',
    })
    try {
      const fileRes = await service.post<
        ApiResponse<{
          id: string
          name: string
          url: string
        }>
      >(
        '/files',
        {
          file,
        },
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )
      if (fileRes.code !== 200001) {
        messageApi.open({
          key: 'upload',
          type: 'error',
          content: fileRes.message,
        })
        return
      }
      const savedRes = await service.put<ApiResponse<unknown>>(
        '/evaluation/yearReport/file',
        {
          id: uploadId,
          files: [fileRes.data],
        }
      )
      if (savedRes.code !== 200001) {
        messageApi.open({
          key: 'upload',
          type: 'error',
          content: savedRes.message,
        })
        return
      }
      messageApi.open({
        key: 'upload',
        type: 'success',
        content: '文件上传成功',
      })
      getTableData()
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      messageApi.open({
        key: 'upload',
        type: 'error',
        content: '上传失败',
      })
    } finally {
      if (fileInput.current) {
        fileInput.current.value = ''
      }
    }
  }

  useEffect(() => {
    getTableData()
  }, [pagination])

  return (
    <div className="flex h-full flex-col">
      <CustomTable
        loading={tableLoading}
        columns={columns}
        dataSource={tableData}
        total={tableTotal}
        pagination={pagination}
        onChange={(page, pageSize) => {
          setPagination({
            ...pagination,
            current: page,
            pageSize,
          })
        }}
      />

      <input
        type="file"
        className="hidden"
        ref={fileInput}
        accept=".doc, .docx, .xls, .pdf, .ppt,pptx, .png, .zip, .tar"
        onChange={handleFileChange}
      />
      {contextHolder}
    </div>
  )
}
