import { Button, message } from 'antd'
import { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import CustomTable, { DataType } from '~/components/table'
import { ApiResponse, service } from '~/lib/service'
import { useAuthStore } from '~/store/auth'

export const Research = () => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  })
  const [tableData, setTableData] = useState<DataType[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [tableLoading, setTableLoading] = useState(false)
  const { userInfo } = useAuthStore()
  const navigate = useNavigate()

  const onFilePreview = async (fileId: string) => {
    message.open({
      type: 'loading',
      content: '请稍后...',
      duration: 0,
    })
    try {
      const res = await service.get<ApiResponse<string>>(
        `files/${fileId}/preview-url`
      )
      if (res.code !== 200001) {
        message.destroy()
        message.open({
          type: 'error',
          content: res.message,
        })
        return
      }
      window.open(res.data, '_blank')
    } finally {
      message.destroy()
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      render: (_text, _record, index) => index + 1,
    },
    {
      title: '调研主题',
      dataIndex: 'name',
      key: 'name',
      width: 100,
    },
    {
      title: '党组织名称',
      dataIndex: 'company',
      key: 'company',
      width: 200,
    },
    {
      title: '中心组成员',
      dataIndex: 'members',
      key: 'members',
      width: 180,
      render: (members: any) =>
        members.map((member: any) => member.full_name).join(', '),
    },
    {
      title: '调研报告',
      dataIndex: 'report',
      key: 'report',
      width: 100,
      align: 'left',
      render: (_, record: any) => (
        <div className="flex break-words">
          {record.report.map((report: any) => (
            <Button
              key={report.id}
              type="link"
              size="large"
              style={{ textDecoration: 'underline', whiteSpace: 'normal' }}
              onClick={() => onFilePreview(report.id)}
            >
              查看
            </Button>
          ))}
        </div>
      ),
    },
    {
      title: '调研简报',
      dataIndex: 'briefing',
      key: 'briefing',
      width: 100,
      align: 'left',
      render: (_, record: any) => (
        <div className="flex break-words">
          {record.briefing.map((briefing: any) => (
            <Button
              key={briefing.id}
              type="link"
              size="large"
              style={{ textDecoration: 'underline', whiteSpace: 'normal' }}
              onClick={() => onFilePreview(briefing.id)}
            >
              查看
            </Button>
          ))}
        </div>
      ),
    },
    {
      title: '材料提交截止时间',
      dataIndex: 'end_at',
      key: 'end_at',
      width: 200,
      align: 'left',
      render: (end_at: any) => end_at && dayjs(end_at).format('YYYY-MM-DD'),
    },
    {
      title: '提醒人',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (_, record) => {
        const hasPermission =
          userInfo?.roles.includes('sa') ||
          userInfo?.roles.includes('qt_undertaker') ||
          record?.secretary?.some((u: any) => u.user_id === userInfo?.id)
        return (
          hasPermission && (
            <Button
              type="link"
              size="large"
              style={{ textDecoration: 'underline' }}
              className="primary-btn-link"
              onClick={() =>
                navigate(`/result/research-report/update/${record.id}`)
              }
            >
              编辑
            </Button>
          )
        )
      },
    },
  ] as ColumnsType<DataType>

  const getTableData = async () => {
    setTableLoading(true)

    try {
      const res = await service.get<
        ApiResponse<{
          list: any[]
          pager: {
            total: number
          }
        }>
      >('/todo/report', { params: pagination })
      if (res.code !== 200001) {
        setTableData([])
        setTableTotal(0)
        message.error(res.message)
        return
      } else {
        setTableData(res.data!.list)
        setTableTotal(res.data!.pager.total)
      }
    } catch (error) {
      message.error('获取数据失败')
      console.log(error)
    } finally {
      setTableLoading(false)
    }
  }

  useEffect(() => {
    getTableData()
  }, [pagination])

  return (
    <div className="flex h-full flex-col">
      <CustomTable
        loading={tableLoading}
        columns={columns}
        dataSource={tableData}
        total={tableTotal}
        pagination={pagination}
        onChange={(page, pageSize) => {
          setPagination({
            ...pagination,
            current: page,
            pageSize,
          })
        }}
      />
    </div>
  )
}
