import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  But<PERSON>,
  Divider,
  Flex,
  Form,
  Input,
  Row,
  Select,
  TreeSelect,
  message,
} from 'antd'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { ROLES } from './role'
import { addUser, editUser, queryOrgTree, queryUserDetail } from '~/apis/user'
import { OrgListItem } from '~/models/user'
import { ApiResponse, service } from '~/lib/service'

const User: React.FC = () => {
  const [form] = Form.useForm()
  const { id } = useParams()
  const navigate = useNavigate()
  const [companies, setCompanies] = useState<OrgListItem[]>([])
  const [users, setUsers] = useState<any[]>([])
  const selectedRoles = Form.useWatch('roles', form)
  const companyId = Form.useWatch('company_id', form)

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const res = await queryOrgTree()
        if (res.code == 200001) {
          setCompanies(res.data)
        } else {
          message.error('获取组织列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])

  useEffect(() => {
    if (!companyId) return
    const fetchOptions = async () => {
      try {
        const response = await service.post<ApiResponse<any[]>>(
          '/users/byRoleAndComId',
          {
            role: ['zxz', 'first_resp'],
            com_id: companyId,
          }
        )
        if (response.code == 200001) {
          setUsers(response.data || [])
        } else {
          message.error('获取人员列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [companyId, form])

  useEffect(() => {
    if (id) {
      queryUserDetail(id).then((res: any) => {
        if (res.code == 200001) {
          form.setFieldsValue({ ...res.data })
        } else {
          message.error('获取用户详情失败')
        }
      })
    }
  }, [form, id])

  /**
   * 删除子角色
   * @param index
   */
  const delSubRole = (index: number) => {
    const subRoles = form.getFieldValue('sub_roles')
    subRoles?.splice(index, 1)
    form.setFieldValue('sub_roles', subRoles)
  }

  /**
   * 添加子角色
   */
  const addSubRole = () => {
    let subRoles = form.getFieldValue('sub_roles')
    if (!subRoles) {
      subRoles = []
    }
    subRoles.push({})
    form.setFieldValue('sub_roles', subRoles)
  }

  /**
   * 提交表单
   */
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values: any) => {
        if (id) {
          // 编辑用户
          editUser({ ...values, id }).then((res: any) => {
            if (res.code == 200001) {
              message.success('编辑成功')
              navigate(`/user-mgt`)
            } else {
              message.error('编辑失败')
            }
          })
        } else {
          // 新增用户
          addUser(values).then((res: any) => {
            if (res.code == 200001) {
              message.success('新增成功')
              navigate(`/user-mgt`)
            } else {
              message.error('新增失败')
            }
          })
        }
      })
      .catch((info: any) => {
        console.log('Validate Failed:', info)
      })
  }

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        items={[
          {
            title: '用户管理',
            href: '#',
            onClick: () => navigate(`/user-mgt`),
          },
          { title: id ? '编辑' : '新增' },
        ]}
      />
      <div className="mt-4 flex h-0 flex-1 flex-col bg-white p-4">
        <p className="mb-4 font-semibold">用户信息</p>
        <Form
          form={form}
          name="form"
          initialValues={{ selfLearningTheme: '' }}
          clearOnDestroy
          autoComplete="off"
          labelCol={{ span: 2 }}
          labelAlign="left"
          className="hide-scrollbar w-full flex-1 overflow-auto overflow-x-hidden"
        >
          <Form.Item
            label="手机号"
            name="mobile"
            rules={[
              {
                required: true,
                message: '请输入手机号!',
              },
            ]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>
          <Form.Item
            label="姓名"
            name="full_name"
            rules={[{ required: true, message: '请输入姓名!' }]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          <Form.Item
            label="组织"
            name="company_id"
            rules={[{ required: true }]}
          >
            <TreeSelect
              onChange={() => {
                form.setFieldValue('for_secretary', [])
              }}
              showSearch
              treeNodeFilterProp="label"
              placeholder="请选择组织"
              allowClear
              treeData={companies}
              fieldNames={{ label: 'name', value: 'id' }}
            />
          </Form.Item>
          <Form.Item label="角色" name="roles" rules={[{ required: true }]}>
            <Select placeholder="请选择角色" mode="multiple" options={ROLES} />
          </Form.Item>
          {selectedRoles && selectedRoles.includes('secretary') && (
            <Form.Item label="分配给领导" name="for_secretary">
              <Select
                placeholder="请选择"
                mode="multiple"
                options={users.map((item) => ({
                  label: item.full_name,
                  value: item.id,
                }))}
              />
            </Form.Item>
          )}
          <Divider style={{ margin: '0 0 24px 0 ' }} />
          <Form.List name="sub_roles">
            {(fields) => {
              return (
                <>
                  {fields.map(({ name, ...restField }, index: number) => (
                    <div>
                      <Form.Item
                        {...restField}
                        label="子角色"
                        name={[name, 'roles']}
                        rules={[{ required: true }]}
                      >
                        <Select
                          placeholder="请选择角色"
                          options={ROLES}
                          mode="multiple"
                        />
                      </Form.Item>
                      <Form.Item
                        label="所属组织"
                        name={[name, 'org_id']}
                        rules={[{ required: true }]}
                      >
                        <TreeSelect
                          showSearch
                          allowClear
                          treeNodeFilterProp="label"
                          placeholder="请选择组织"
                          treeData={companies}
                          fieldNames={{ label: 'name', value: 'id' }}
                        />
                      </Form.Item>
                      <Button
                        style={{ marginBottom: '24px' }}
                        onClick={() => {
                          delSubRole(index)
                        }}
                      >
                        删除
                      </Button>
                      <Divider style={{ margin: '0 0 24px 0 ' }} />
                    </div>
                  ))}
                </>
              )
            }}
          </Form.List>
          <Row style={{ marginBottom: '24px' }}>
            <Button
              onClick={() => {
                addSubRole()
              }}
            >
              添加子角色
            </Button>
          </Row>
        </Form>

        <Flex className="my-4" gap="small" wrap justify="end">
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn-outlined"
            onClick={() => navigate(`/user-mgt`)}
          >
            取消
          </Button>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="secondary-btn"
            onClick={() => handleSubmit()}
          >
            保存
          </Button>
        </Flex>
      </div>
    </div>
  )
}

export default User
