import {
  Space,
  Select,
  Cascader,
  <PERSON>read<PERSON>rumb,
  Button,
  Flex,
  Form,
  message,
  Input,
  Modal,
} from 'antd'
import { ColumnsType } from 'antd/es/table'
import { useEffect, useState } from 'react'
import { useAsync } from 'react-use'
import { useNavigate } from 'react-router-dom'
import { ROLES } from './role'
import { deleteUser, queryOrgTree, queryUserMgtList } from '~/apis/user'
import Permission from '~/components/permission'
import CustomTable, { DataType } from '~/components/table'
import { UserMgtList, UserMgtListParams } from '~/models/user'

/**
 * 用户管理设置
 * @returns
 */
const UserMgt: React.FC = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [tableData, setTableData] = useState<UserMgtList[]>([])
  const [tableTotal, setTableTotal] = useState(0)
  const [companies, setCompanies] = useState<any[]>([])

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const res = await queryOrgTree()
        if (res.code == 200001) {
          setCompanies(res.data)
        } else {
          message.error('获取组织列表失败')
        }
      } catch (error) {
        console.error('Error fetching options:', error)
      }
    }

    fetchOptions()
  }, [])

  // 表格表头
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      render: (_, _record, index) => index + 1,
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
    },
    {
      title: '姓名',
      dataIndex: 'full_name',
    },
    {
      title: '所属组织',
      dataIndex: 'company_list',
      render: (_: unknown, record: UserMgtList) =>
        record?.company_list?.map((item: string) => {
          return <div>{item}</div>
        }),
      renderFormItem: () => (
        <Cascader
          showSearch
          options={companies}
          changeOnSelect
          expandTrigger="hover"
        />
      ),
    },
    {
      title: '角色',
      dataIndex: 'role_list',
      render: (_: unknown, record: UserMgtList) =>
        record?.role_list?.map((item: string) => {
          return <div>{item}</div>
        }),
      renderFormItem: () => (
        <Select
          showSearch
          options={ROLES}
          filterOption={(input: string, option: any) =>
            option?.label?.includes(input)
          }
        />
      ),
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_: unknown, record: UserMgtList) => (
        <Space>
          <Button
            type="link"
            size="large"
            className="primary-btn-link"
            onClick={() => navigate(`update/${record.id}`)}
          >
            编辑
          </Button>

          <Button
            type="link"
            size="large"
            className="danger-btn-link"
            onClick={() => handleRowDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ] as ColumnsType<DataType>

  // 删除数据二次确认
  const { confirm } = Modal
  const handleRowDelete = (record: UserMgtList) => {
    confirm({
      title: '确定要删除该数据吗？',
      content: '删除数据：【' + record.full_name + '】',
      okText: '删除',
      cancelText: '取消',
      onOk() {
        deleteUser(record.id).then((res) => {
          if (res?.code === 200001) {
            message.success('删除成功')
            getTableData({ current: 1, pageSize: 10 })
          } else {
            message.error('删除失败 ' + res?.message)
          }
        })
      },
    })
  }

  const getTableData = (params: UserMgtListParams) => {
    setLoading(true)
    queryUserMgtList(params)
      .then((res) => {
        if (res.code !== 200001) {
          setTableData([])
          setTableTotal(0)
        } else {
          setTableData(res.data.list)
          setTableTotal(res.data.pager.total)
        }
        setLoading(false)
      })
      .catch(() => {
        message.error('获取用户列表失败')
        setLoading(false)
      })
  }

  useAsync(async () => {
    await getTableData({ current: 1, pageSize: 10 })
  })

  return (
    <div className="flex h-full flex-col">
      <Breadcrumb
        separator=">"
        className="mb-4"
        items={[
          {
            title: '用户管理',
            href: '#',
          },
        ]}
      />
      <Permission roles={['sa']} codes={[]}>
        <Flex className="mb-4" gap="small" wrap>
          <Form form={form} name="form" layout="inline">
            <Form.Item label="姓名" name="full_name">
              <Input type="text" />
            </Form.Item>
            <Form.Item label="手机号" name="mobile">
              <Input type="text" />
            </Form.Item>
            <Form.Item label="所属组织" name="company_id">
              <Cascader
                showSearch
                options={companies}
                changeOnSelect
                expandTrigger="hover"
                style={{ width: 250 }}
                fieldNames={{ label: 'name', value: 'id' }}
              />
            </Form.Item>
            <Form.Item label="角色" name="role_name">
              <Select
                showSearch
                options={ROLES}
                filterOption={(input: string, option: any) =>
                  option?.label?.includes(input)
                }
                style={{ width: 150 }}
              />
            </Form.Item>
            <Form.Item>
              <Button
                color="primary"
                variant="solid"
                size="large"
                className="primary-btn"
                onClick={() => {
                  const { company_id } = form.getFieldsValue()

                  getTableData({
                    ...form.getFieldsValue(),
                    company_id: company_id[company_id.length - 1],
                    current: 1,
                    pageSize: 10,
                  })
                }}
              >
                查询
              </Button>
              <Button
                color="primary"
                variant="solid"
                size="large"
                className="secondary-btn"
                onClick={() => {
                  form.resetFields()
                  getTableData({ current: 1, pageSize: 10 })
                }}
                style={{ marginLeft: '10px' }}
              >
                重置
              </Button>
            </Form.Item>
          </Form>
        </Flex>
        <Flex className="mb-4" gap="small" wrap>
          <Button
            color="primary"
            variant="solid"
            size="large"
            className="primary-btn"
            onClick={() => navigate(`create`)}
          >
            新增用户
          </Button>
        </Flex>
        <CustomTable
          loading={loading}
          columns={columns}
          dataSource={tableData}
          rowSelection={{ type: 'checkbox' }}
          total={tableTotal}
          onChange={(page, pageSize) => {
            getTableData({ current: page, pageSize: pageSize })
          }}
        ></CustomTable>
      </Permission>
    </div>
  )
}

export default UserMgt
